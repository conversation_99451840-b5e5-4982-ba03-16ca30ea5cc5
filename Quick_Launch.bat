@echo off
title MW2 GamePass Trainer - Quick Launch
color 0A

echo ╔══════════════════════════════════════════════════════════╗
echo ║          MW2 2022 GamePass Trainer - Quick Launch       ║
echo ╚══════════════════════════════════════════════════════════╝
echo.

REM Check if we need to compile
if not exist "MW2_Standalone_Trainer.exe" (
    echo [*] First time setup - need to compile trainer...
    echo [*] Checking for compilers...
    
    where g++ >nul 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo [+] Found MinGW, compiling...
        g++ -std=c++17 -O2 -static -o MW2_Standalone_Trainer.exe MW2_Standalone_Trainer.cpp -lpsapi
        if %ERRORLEVEL% EQU 0 (
            echo [+] Compilation successful!
            goto :launch
        )
    )
    
    where cl >nul 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo [+] Found Visual Studio, compiling...
        cl /EHsc /O2 MW2_Standalone_Trainer.cpp /Fe:MW2_Standalone_Trainer.exe
        if %ERRORLEVEL% EQU 0 (
            echo [+] Compilation successful!
            goto :launch
        )
    )
    
    echo [!] No compiler found. Creating simple launcher instead...
    goto :create_simple
)

:launch
echo [*] Launching MW2 Standalone Trainer...
echo.
echo [!] IMPORTANT:
echo     • Make sure MW2 is running from Xbox App
echo     • Use OFFLINE/Survival mode ONLY
echo     • This must run as Administrator
echo.
echo [*] Press any key to launch trainer...
pause >nul

if exist "MW2_Standalone_Trainer.exe" (
    start "" "MW2_Standalone_Trainer.exe"
) else (
    goto :create_simple
)
goto :end

:create_simple
echo [*] Creating simple memory patcher...
echo.

REM Create a simple PowerShell-based trainer
echo # MW2 GamePass Simple Trainer > SimpleTrainer.ps1
echo Add-Type -TypeDefinition @" >> SimpleTrainer.ps1
echo using System; >> SimpleTrainer.ps1
echo using System.Diagnostics; >> SimpleTrainer.ps1
echo using System.Runtime.InteropServices; >> SimpleTrainer.ps1
echo public class MemoryHelper { >> SimpleTrainer.ps1
echo     [DllImport("kernel32.dll")] >> SimpleTrainer.ps1
echo     public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId); >> SimpleTrainer.ps1
echo     [DllImport("kernel32.dll")] >> SimpleTrainer.ps1
echo     public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead); >> SimpleTrainer.ps1
echo     [DllImport("kernel32.dll")] >> SimpleTrainer.ps1
echo     public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int nSize, out int lpNumberOfBytesWritten); >> SimpleTrainer.ps1
echo } >> SimpleTrainer.ps1
echo "@ >> SimpleTrainer.ps1
echo. >> SimpleTrainer.ps1
echo Write-Host "MW2 GamePass Simple Trainer" -ForegroundColor Green >> SimpleTrainer.ps1
echo Write-Host "Searching for MW2 process..." >> SimpleTrainer.ps1
echo. >> SimpleTrainer.ps1
echo $process = Get-Process ^| Where-Object {$_.ProcessName -like "*cod22*" -or $_.ProcessName -like "*ModernWarfare*"} >> SimpleTrainer.ps1
echo if ($process) { >> SimpleTrainer.ps1
echo     Write-Host "Found MW2 process: $($process.ProcessName) (PID: $($process.Id))" -ForegroundColor Green >> SimpleTrainer.ps1
echo     Write-Host "Trainer attached! Use Cheat Engine for advanced features." >> SimpleTrainer.ps1
echo } else { >> SimpleTrainer.ps1
echo     Write-Host "MW2 process not found. Make sure the game is running." -ForegroundColor Red >> SimpleTrainer.ps1
echo } >> SimpleTrainer.ps1
echo Read-Host "Press Enter to exit" >> SimpleTrainer.ps1

echo [+] Created SimpleTrainer.ps1
echo [*] Launching simple trainer...
powershell -ExecutionPolicy Bypass -File SimpleTrainer.ps1
goto :end

:end
echo.
echo [*] For full features, install a C++ compiler:
echo     • MinGW-w64: https://www.mingw-w64.org/
echo     • Visual Studio Community: https://visualstudio.microsoft.com/vs/community/
echo.
pause
