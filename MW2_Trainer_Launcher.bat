@echo off
title MW2 2022 GamePass Trainer Launcher
color 0A

:main_menu
cls
echo ╔══════════════════════════════════════════════════════════╗
echo ║          MW2 2022 GamePass Trainer Launcher             ║
echo ║                    No Cheat Engine!                     ║
echo ╚══════════════════════════════════════════════════════════╝
echo.
echo [*] Current Status:
if exist "MW2_Standalone_Trainer.exe" (
    echo     ✓ Trainer: READY
) else (
    echo     ✗ Trainer: NOT COMPILED
)

if exist "MW2_Scanner.exe" (
    echo     ✓ Scanner: READY
) else (
    echo     ✗ Scanner: NOT COMPILED
)

echo.
echo ╔══════════════════════════════════════════════════════════╗
echo ║                        OPTIONS                           ║
echo ╠══════════════════════════════════════════════════════════╣
echo ║  [1] 🚀 Launch MW2 Standalone Trainer                   ║
echo ║  [2] 🔍 Launch Memory Scanner                           ║
echo ║  [3] 🔨 Compile Tools (First Time Setup)               ║
echo ║  [4] 📖 View Instructions                               ║
echo ║  [5] ⚠️  Safety Guidelines                              ║
echo ║  [0] 🚪 Exit                                            ║
echo ╚══════════════════════════════════════════════════════════╝
echo.
set /p choice="Select option: "

if "%choice%"=="1" goto :launch_trainer
if "%choice%"=="2" goto :launch_scanner
if "%choice%"=="3" goto :compile
if "%choice%"=="4" goto :instructions
if "%choice%"=="5" goto :safety
if "%choice%"=="0" goto :exit
goto :main_menu

:launch_trainer
cls
echo [*] Launching MW2 Standalone Trainer...
echo.
if not exist "MW2_Standalone_Trainer.exe" (
    echo [!] Trainer not found! Please compile first (option 3)
    pause
    goto :main_menu
)

echo [*] Make sure:
echo     • MW2 is running from Xbox App
echo     • You're in OFFLINE/Survival mode
echo     • This launcher is running as Administrator
echo.
echo [*] Press any key to launch trainer...
pause >nul

start "" "MW2_Standalone_Trainer.exe"
goto :main_menu

:launch_scanner
cls
echo [*] Launching Memory Scanner...
echo.
if not exist "MW2_Scanner.exe" (
    echo [!] Scanner not found! Please compile first (option 3)
    pause
    goto :main_menu
)

start "" "MW2_Scanner.exe"
goto :main_menu

:compile
cls
echo [*] Compiling tools...
echo.
call compile_tools.bat
pause
goto :main_menu

:instructions
cls
echo ╔══════════════════════════════════════════════════════════╗
echo ║                      INSTRUCTIONS                        ║
echo ╚══════════════════════════════════════════════════════════╝
echo.
echo 🎯 QUICK START:
echo ═══════════════
echo 1. First time: Choose option [3] to compile tools
echo 2. Launch MW2 from Xbox App in OFFLINE mode
echo 3. Choose option [1] to launch the trainer
echo 4. Use number keys to toggle features
echo.
echo 🎮 AVAILABLE FEATURES:
echo ═══════════════════════
echo • God Mode - Never die
echo • Infinite Ammo - Never reload
echo • No Recoil - Perfect accuracy
echo • Rapid Fire - Faster shooting
echo • Auto Health Detection - Finds health automatically
echo.
echo 🔧 TROUBLESHOOTING:
echo ═══════════════════
echo • "Process not found" → Make sure MW2 is running
echo • "Access denied" → Run this launcher as Administrator
echo • "Features don't work" → Game may have updated
echo.
echo Press any key to return to menu...
pause >nul
goto :main_menu

:safety
cls
echo ╔══════════════════════════════════════════════════════════╗
echo ║                    SAFETY GUIDELINES                     ║
echo ╚══════════════════════════════════════════════════════════╝
echo.
echo ✅ SAFE TO USE IN:
echo ═══════════════════
echo • Campaign Mode (Single-player)
echo • Survival Mode (Offline co-op)
echo • Private/Local matches
echo • Any offline content
echo.
echo ❌ NEVER USE IN:
echo ═══════════════
echo • Multiplayer (Online matches)
echo • Warzone (Battle Royale)
echo • Any online mode
echo • When connected to internet
echo.
echo 🛡️ EXTRA SAFETY:
echo ═══════════════
echo • Disconnect internet when using trainer
echo • Only use in offline modes
echo • Don't stream/record with cheats visible
echo • Keep tools private
echo.
echo ⚠️ WARNING: Using cheats online = PERMANENT BAN
echo.
echo Press any key to return to menu...
pause >nul
goto :main_menu

:exit
cls
echo [*] Thanks for using MW2 GamePass Trainer!
echo [*] Remember: Only use in offline modes!
echo.
timeout /t 2 >nul
exit

:error
echo [!] An error occurred. Please try again.
pause
goto :main_menu
