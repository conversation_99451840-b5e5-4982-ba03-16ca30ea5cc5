# 🎮 MW2 2022 GamePass Standalone Trainer

**No Cheat Engine Required!** - Complete standalone trainer for Call of Duty: Modern Warfare II (2022) GamePass version.

## 🚀 Quick Start (3 Steps)

1. **Right-click `MW2_Trainer_Launcher.bat` → "Run as Administrator"**
2. **Choose option [3] to compile tools (first time only)**
3. **Launch MW2 in offline mode, then choose option [1]**

That's it! The trainer handles everything automatically.

## ✨ Features

### 🎯 Main Trainer (`MW2_Standalone_Trainer.exe`)
- **🛡️ God Mode** - Never die, auto-detects health
- **🔫 Infinite Ammo** - Never run out of bullets
- **🎯 No Recoil** - Perfect weapon accuracy
- **⚡ Rapid Fire** - Faster shooting speed
- **🔍 Auto-Detection** - Finds MW2 process automatically
- **🎨 Beautiful Interface** - Clean menu system

### 🔍 Memory Scanner (`MW2_Scanner.exe`)
- Find current memory addresses
- Debug tool for advanced users
- GamePass process detection

## 📁 What You Get

```
MW2_GamePass_Trainer/
├── 🚀 MW2_Trainer_Launcher.bat      # Main launcher (START HERE)
├── 🔨 compile_tools.bat             # Auto-compiler
├── 📝 MW2_Standalone_Trainer.cpp    # Main trainer source
├── 🔍 MW2_GamePass_Scanner.cpp      # Scanner source
├── 📖 README.md                     # This file
└── 📚 Various guides and docs
```

## 🎮 GamePass Specific Features

- ✅ **UWP App Support** - Handles GamePass permissions
- ✅ **Multiple Process Names** - Detects various MW2 executables
- ✅ **Auto-Admin Check** - Warns if not running as administrator
- ✅ **Permission Handling** - Tries different access levels
- ✅ **GamePass Troubleshooting** - Built-in help system

## 🛡️ Safety First

### ✅ Safe Modes:
- **Campaign** (Single-player story)
- **Survival** (Offline co-op)
- **Private matches**
- **Any offline content**

### ❌ Never Use In:
- **Multiplayer** (Online matches)
- **Warzone** (Battle Royale)
- **Any online mode**

**⚠️ Using online = PERMANENT ACCOUNT BAN**

## 🔧 Troubleshooting

### "Process not found"
- Make sure MW2 is running from Xbox App
- Verify you're in offline/survival mode
- Check Task Manager for exact process name

### "Access denied"
- Run launcher as Administrator
- Disable Windows Defender temporarily
- Make sure MW2 is fully loaded

### "Features don't work"
- Game may have updated (offsets changed)
- Try restarting both MW2 and trainer
- Check if you're truly in offline mode

## 🎯 How It Works

1. **Process Detection**: Automatically finds MW2 GamePass process
2. **Memory Access**: Attaches with appropriate permissions
3. **Address Finding**: Scans for health/ammo values dynamically
4. **Feature Toggle**: Modifies memory in real-time
5. **Safe Cleanup**: Restores original values on exit

## 🔄 Updates

The trainer includes known offsets from the UnknownCheats forum but also:
- **Dynamic scanning** for health addresses
- **Automatic process detection** for different MW2 versions
- **Fallback methods** when offsets change

## 💡 Advanced Usage

### Finding New Addresses:
1. Use the built-in scanner
2. Look for your current health value
3. Take damage and scan again
4. Repeat until address is found

### Custom Modifications:
- Edit the source code for new features
- Recompile with the provided script
- Add your own memory patterns

## 🏆 Why This Trainer?

- **No Cheat Engine** - Completely standalone
- **GamePass Optimized** - Built specifically for Xbox App version
- **Auto-Detection** - Finds addresses automatically
- **Safe Design** - Only works in offline modes
- **User Friendly** - Beautiful interface with clear instructions
- **Open Source** - Full source code included

## 📞 Support

If something doesn't work:
1. Check the troubleshooting section
2. Make sure you're running as Administrator
3. Verify MW2 is in offline mode
4. Try recompiling the tools

## ⚖️ Legal

This trainer is for:
- **Educational purposes**
- **Offline single-player use only**
- **Personal enjoyment**

**Not for:**
- Online multiplayer
- Competitive advantage
- Distribution of cheats

Use at your own risk. We are not responsible for any account bans or issues.

---

**🎮 Enjoy your enhanced MW2 offline experience!**
