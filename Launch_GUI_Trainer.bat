@echo off
title MW2 GUI Trainer Launcher
color 0A

echo ╔══════════════════════════════════════════════════════════╗
echo ║          MW2 2022 GamePass GUI Trainer Launcher         ║
echo ╚══════════════════════════════════════════════════════════╝
echo.
echo [*] Launching GUI Trainer...
echo [*] A Windows Forms window should appear shortly
echo.
echo [!] IMPORTANT:
echo     • Make sure MW2 is running from Xbox App
echo     • Use OFFLINE/Survival mode ONLY
echo     • Run this as Administrator for best results
echo.
echo [*] Starting GUI...

powershell -ExecutionPolicy Bypass -WindowStyle Hidden -File "MW2_GUI_Trainer.ps1"

echo.
echo [*] GUI Trainer closed.
pause
