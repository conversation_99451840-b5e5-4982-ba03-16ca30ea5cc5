# MW2 2022 GamePass PowerShell Trainer
# No compilation required!

Add-Type -TypeDefinition @"
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

public class MemoryHelper {
    [DllImport("kernel32.dll")]
    public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId);
    
    [DllImport("kernel32.dll")]
    public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead);
    
    [DllImport("kernel32.dll")]
    public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int nSize, out int lpNumberOfBytesWritten);
    
    [DllImport("kernel32.dll")]
    public static extern bool CloseHandle(IntPtr hObject);
    
    [DllImport("kernel32.dll")]
    public static extern bool VirtualQueryEx(IntPtr hProcess, IntPtr lpAddress, out MEMORY_BASIC_INFORMATION lpBuffer, uint dwLength);
    
    [StructLayout(LayoutKind.Sequential)]
    public struct MEMORY_BASIC_INFORMATION {
        public IntPtr BaseAddress;
        public IntPtr AllocationBase;
        public uint AllocationProtect;
        public IntPtr RegionSize;
        public uint State;
        public uint Protect;
        public uint Type;
    }
    
    public const int PROCESS_ALL_ACCESS = 0x1F0FFF;
    public const int PROCESS_VM_READ = 0x0010;
    public const int PROCESS_VM_WRITE = 0x0020;
    public const int PROCESS_VM_OPERATION = 0x0008;
    public const int PROCESS_QUERY_INFORMATION = 0x0400;
}
"@

class MW2Trainer {
    [IntPtr]$ProcessHandle
    [int]$ProcessId
    [IntPtr]$BaseAddress
    [bool]$GodModeActive = $false
    [bool]$InfiniteAmmoActive = $false
    [bool]$NoRecoilActive = $false
    
    MW2Trainer() {
        $this.ProcessHandle = [IntPtr]::Zero
        $this.ProcessId = 0
        $this.BaseAddress = [IntPtr]::Zero
    }
    
    [bool] FindMW2Process() {
        Write-Host "[*] Searching for MW2 process..." -ForegroundColor Yellow
        
        $processNames = @("cod22-cod", "ModernWarfareII", "Call of Duty Modern Warfare II")
        
        foreach ($name in $processNames) {
            $processes = Get-Process -Name $name -ErrorAction SilentlyContinue
            if ($processes) {
                $this.ProcessId = $processes[0].Id
                Write-Host "[+] Found MW2 process: $($processes[0].ProcessName) (PID: $($this.ProcessId))" -ForegroundColor Green
                return $this.AttachToProcess()
            }
        }
        
        # Try partial matches
        $processes = Get-Process | Where-Object { $_.ProcessName -like "*cod*" -and $_.ProcessName -notlike "*cloudcode*" }
        if ($processes) {
            $this.ProcessId = $processes[0].Id
            Write-Host "[+] Found potential MW2 process: $($processes[0].ProcessName) (PID: $($this.ProcessId))" -ForegroundColor Green
            return $this.AttachToProcess()
        }
        
        Write-Host "[-] MW2 process not found!" -ForegroundColor Red
        return $false
    }
    
    [bool] AttachToProcess() {
        $permissions = @(
            [MemoryHelper]::PROCESS_ALL_ACCESS,
            ([MemoryHelper]::PROCESS_VM_READ -bor [MemoryHelper]::PROCESS_VM_WRITE -bor [MemoryHelper]::PROCESS_VM_OPERATION -bor [MemoryHelper]::PROCESS_QUERY_INFORMATION),
            ([MemoryHelper]::PROCESS_VM_READ -bor [MemoryHelper]::PROCESS_VM_WRITE -bor [MemoryHelper]::PROCESS_VM_OPERATION)
        )
        
        foreach ($perm in $permissions) {
            $this.ProcessHandle = [MemoryHelper]::OpenProcess($perm, $false, $this.ProcessId)
            if ($this.ProcessHandle -ne [IntPtr]::Zero) {
                Write-Host "[+] Successfully attached to process!" -ForegroundColor Green
                $this.GetBaseAddress()
                return $true
            }
        }
        
        Write-Host "[-] Failed to attach to process. Try running as Administrator." -ForegroundColor Red
        return $false
    }
    
    [void] GetBaseAddress() {
        try {
            $process = Get-Process -Id $this.ProcessId
            $this.BaseAddress = $process.MainModule.BaseAddress
            Write-Host "[+] Base Address: 0x$($this.BaseAddress.ToString('X'))" -ForegroundColor Green
        } catch {
            Write-Host "[-] Could not get base address" -ForegroundColor Red
        }
    }
    
    [byte[]] ReadMemory([IntPtr]$address, [int]$size) {
        $buffer = New-Object byte[] $size
        $bytesRead = 0
        $success = [MemoryHelper]::ReadProcessMemory($this.ProcessHandle, $address, $buffer, $size, [ref]$bytesRead)
        if ($success -and $bytesRead -eq $size) {
            return $buffer
        }
        return $null
    }
    
    [bool] WriteMemory([IntPtr]$address, [byte[]]$data) {
        $bytesWritten = 0
        return [MemoryHelper]::WriteProcessMemory($this.ProcessHandle, $address, $data, $data.Length, [ref]$bytesWritten)
    }
    
    [void] ToggleGodMode() {
        $this.GodModeActive = -not $this.GodModeActive
        $status = if ($this.GodModeActive) { "ENABLED" } else { "DISABLED" }
        Write-Host "[*] God Mode: $status" -ForegroundColor $(if ($this.GodModeActive) { "Green" } else { "Yellow" })
        
        if ($this.GodModeActive) {
            Write-Host "[!] God Mode activated. Find your health address using memory scanning." -ForegroundColor Cyan
            Write-Host "[*] Instructions:" -ForegroundColor White
            Write-Host "    1. Note your current health (e.g., 100)" -ForegroundColor White
            Write-Host "    2. Take damage and note new health (e.g., 75)" -ForegroundColor White
            Write-Host "    3. Use Cheat Engine to scan for these values" -ForegroundColor White
        }
    }
    
    [void] ToggleInfiniteAmmo() {
        $this.InfiniteAmmoActive = -not $this.InfiniteAmmoActive
        $status = if ($this.InfiniteAmmoActive) { "ENABLED" } else { "DISABLED" }
        Write-Host "[*] Infinite Ammo: $status" -ForegroundColor $(if ($this.InfiniteAmmoActive) { "Green" } else { "Yellow" })
        
        if ($this.InfiniteAmmoActive) {
            Write-Host "[!] Infinite Ammo activated. This is a placeholder implementation." -ForegroundColor Cyan
            Write-Host "[*] For full functionality, use the compiled C++ trainer." -ForegroundColor White
        }
    }
    
    [void] ToggleNoRecoil() {
        $this.NoRecoilActive = -not $this.NoRecoilActive
        $status = if ($this.NoRecoilActive) { "ENABLED" } else { "DISABLED" }
        Write-Host "[*] No Recoil: $status" -ForegroundColor $(if ($this.NoRecoilActive) { "Green" } else { "Yellow" })
        
        if ($this.NoRecoilActive) {
            # Try to apply no recoil using known offset
            $recoilOffset = 0x48C84
            $recoilAddress = [IntPtr]::Add($this.BaseAddress, $recoilOffset)
            $noRecoilBytes = [BitConverter]::GetBytes([float]0.0)
            
            if ($this.WriteMemory($recoilAddress, $noRecoilBytes)) {
                Write-Host "[+] No recoil applied successfully!" -ForegroundColor Green
            } else {
                Write-Host "[-] Failed to apply no recoil (offset may be outdated)" -ForegroundColor Red
                $this.NoRecoilActive = $false
            }
        }
    }
    
    [void] ShowMenu() {
        Clear-Host
        Write-Host "╔══════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
        Write-Host "║          MW2 2022 GamePass PowerShell Trainer           ║" -ForegroundColor Cyan
        Write-Host "╠══════════════════════════════════════════════════════════╣" -ForegroundColor Cyan
        
        $processStatus = if ($this.ProcessHandle -ne [IntPtr]::Zero) { "CONNECTED" } else { "DISCONNECTED" }
        $processColor = if ($this.ProcessHandle -ne [IntPtr]::Zero) { "Green" } else { "Red" }
        
        Write-Host "║ Process: " -ForegroundColor Cyan -NoNewline
        Write-Host ("{0,-47}" -f $processStatus) -ForegroundColor $processColor -NoNewline
        Write-Host " ║" -ForegroundColor Cyan
        
        Write-Host "║ PID: " -ForegroundColor Cyan -NoNewline
        Write-Host ("{0,-51}" -f $this.ProcessId) -ForegroundColor White -NoNewline
        Write-Host " ║" -ForegroundColor Cyan
        
        Write-Host "║ Base: 0x" -ForegroundColor Cyan -NoNewline
        Write-Host ("{0,-45}" -f $this.BaseAddress.ToString('X')) -ForegroundColor White -NoNewline
        Write-Host " ║" -ForegroundColor Cyan
        
        Write-Host "╠══════════════════════════════════════════════════════════╣" -ForegroundColor Cyan
        Write-Host "║                        FEATURES                          ║" -ForegroundColor Cyan
        Write-Host "╠══════════════════════════════════════════════════════════╣" -ForegroundColor Cyan
        
        $godStatus = if ($this.GodModeActive) { "ON " } else { "OFF" }
        $ammoStatus = if ($this.InfiniteAmmoActive) { "ON " } else { "OFF" }
        $recoilStatus = if ($this.NoRecoilActive) { "ON " } else { "OFF" }
        
        Write-Host "║ [1] God Mode:           " -ForegroundColor Cyan -NoNewline
        Write-Host ("{0,30}" -f $godStatus) -ForegroundColor $(if ($this.GodModeActive) { "Green" } else { "Yellow" }) -NoNewline
        Write-Host " ║" -ForegroundColor Cyan
        
        Write-Host "║ [2] Infinite Ammo:      " -ForegroundColor Cyan -NoNewline
        Write-Host ("{0,30}" -f $ammoStatus) -ForegroundColor $(if ($this.InfiniteAmmoActive) { "Green" } else { "Yellow" }) -NoNewline
        Write-Host " ║" -ForegroundColor Cyan
        
        Write-Host "║ [3] No Recoil:          " -ForegroundColor Cyan -NoNewline
        Write-Host ("{0,30}" -f $recoilStatus) -ForegroundColor $(if ($this.NoRecoilActive) { "Green" } else { "Yellow" }) -NoNewline
        Write-Host " ║" -ForegroundColor Cyan
        
        Write-Host "║ [4] Process Info:       " -ForegroundColor Cyan -NoNewline
        Write-Host ("{0,30}" -f "Show Details") -ForegroundColor White -NoNewline
        Write-Host " ║" -ForegroundColor Cyan
        
        Write-Host "║ [0] Exit Trainer        " -ForegroundColor Cyan -NoNewline
        Write-Host ("{0,30}" -f "") -ForegroundColor White -NoNewline
        Write-Host " ║" -ForegroundColor Cyan
        
        Write-Host "╚══════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
        Write-Host ""
    }
    
    [void] ShowProcessInfo() {
        if ($this.ProcessId -eq 0) {
            Write-Host "[!] No process attached" -ForegroundColor Red
            return
        }
        
        try {
            $process = Get-Process -Id $this.ProcessId
            Write-Host "[*] Process Information:" -ForegroundColor Yellow
            Write-Host "    Name: $($process.ProcessName)" -ForegroundColor White
            Write-Host "    PID: $($process.Id)" -ForegroundColor White
            Write-Host "    Memory Usage: $([math]::Round($process.WorkingSet64 / 1MB, 2)) MB" -ForegroundColor White
            Write-Host "    Base Address: 0x$($this.BaseAddress.ToString('X'))" -ForegroundColor White
            Write-Host "    Handle: 0x$($this.ProcessHandle.ToString('X'))" -ForegroundColor White
        } catch {
            Write-Host "[-] Could not get process information" -ForegroundColor Red
        }
    }
    
    [void] Run() {
        Write-Host "MW2 2022 GamePass PowerShell Trainer" -ForegroundColor Green
        Write-Host "=====================================" -ForegroundColor Green
        Write-Host ""
        
        if (-not $this.FindMW2Process()) {
            Write-Host ""
            Write-Host "[!] MW2 process not found!" -ForegroundColor Red
            Write-Host "[*] Make sure:" -ForegroundColor Yellow
            Write-Host "    - MW2 is running from Xbox App" -ForegroundColor White
            Write-Host "    - You're in offline/survival mode" -ForegroundColor White
            Write-Host "    - This script is running as Administrator" -ForegroundColor White
            Read-Host "Press Enter to exit"
            return
        }
        
        while ($true) {
            $this.ShowMenu()
            $choice = Read-Host "Select option"
            
            switch ($choice) {
                "1" { $this.ToggleGodMode() }
                "2" { $this.ToggleInfiniteAmmo() }
                "3" { $this.ToggleNoRecoil() }
                "4" { $this.ShowProcessInfo() }
                "0" { 
                    Write-Host "[*] Exiting trainer..." -ForegroundColor Yellow
                    if ($this.ProcessHandle -ne [IntPtr]::Zero) {
                        [MemoryHelper]::CloseHandle($this.ProcessHandle)
                    }
                    return 
                }
                default { Write-Host "[!] Invalid option!" -ForegroundColor Red }
            }
            
            if ($choice -ne "0") {
                Write-Host ""
                Read-Host "Press Enter to continue"
            }
        }
    }
}

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "[!] WARNING: Not running as Administrator!" -ForegroundColor Red
    Write-Host "[!] GamePass apps may require elevated permissions." -ForegroundColor Red
    Write-Host "[*] Press Enter to continue anyway..." -ForegroundColor Yellow
    Read-Host
}

# Create and run trainer
$trainer = [MW2Trainer]::new()
$trainer.Run()
