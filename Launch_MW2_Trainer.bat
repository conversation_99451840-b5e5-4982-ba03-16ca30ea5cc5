@echo off
title MW2 GamePass Trainer Launcher
color 0A

echo ╔══════════════════════════════════════════════════════════╗
echo ║          MW2 2022 GamePass Trainer Launcher             ║
echo ║                 Ready to Launch!                        ║
echo ╚══════════════════════════════════════════════════════════╝
echo.

echo [*] Available Trainers:
echo.
echo [1] 🚀 PowerShell Trainer (No compilation needed)
echo [2] 🔍 Process Finder (Check if MW2 is running)
echo [3] 📖 Instructions
echo [0] 🚪 Exit
echo.
set /p choice="Select option: "

if "%choice%"=="1" goto :powershell_trainer
if "%choice%"=="2" goto :process_finder
if "%choice%"=="3" goto :instructions
if "%choice%"=="0" goto :exit
goto :main

:powershell_trainer
cls
echo [*] Launching PowerShell Trainer...
echo.
echo [!] IMPORTANT:
echo     • Make sure MW2 is running from Xbox App
echo     • Use OFFLINE/Survival mode ONLY
echo     • This launcher should be running as Administrator
echo.
echo [*] Press any key to launch PowerShell trainer...
pause >nul

powershell -ExecutionPolicy Bypass -File "MW2_PowerShell_Trainer.ps1"
goto :main

:process_finder
cls
echo [*] Launching Process Finder...
echo.
start "" "MW2_Process_Finder.bat"
goto :main

:instructions
cls
echo ╔══════════════════════════════════════════════════════════╗
echo ║                      INSTRUCTIONS                        ║
echo ╚══════════════════════════════════════════════════════════╝
echo.
echo 🎯 QUICK START:
echo ═══════════════
echo 1. Launch MW2 from Xbox App in OFFLINE mode
echo 2. Go to Survival mode
echo 3. Run this launcher as Administrator
echo 4. Choose option [1] for PowerShell trainer
echo.
echo 🎮 TRAINER FEATURES:
echo ═══════════════════════
echo • God Mode - Never die (placeholder)
echo • Infinite Ammo - Never reload (placeholder)
echo • No Recoil - Perfect accuracy (working)
echo • Process Detection - Finds MW2 automatically
echo.
echo 🛡️ SAFETY:
echo ═════════
echo • ONLY use in offline modes
echo • NEVER use online (permanent ban)
echo • Run as Administrator for best results
echo.
echo 💡 NOTES:
echo ═════════
echo • PowerShell trainer works without compilation
echo • For full features, install a C++ compiler
echo • Some features are placeholders in PowerShell version
echo.
pause
goto :main

:exit
echo [*] Thanks for using MW2 GamePass Trainer!
echo [*] Remember: Only use in offline modes!
timeout /t 2 >nul
exit

:main
cls
goto :start

:start
echo ╔══════════════════════════════════════════════════════════╗
echo ║          MW2 2022 GamePass Trainer Launcher             ║
echo ║                 Ready to Launch!                        ║
echo ╚══════════════════════════════════════════════════════════╝
echo.

echo [*] Available Trainers:
echo.
echo [1] 🚀 PowerShell Trainer (No compilation needed)
echo [2] 🔍 Process Finder (Check if MW2 is running)
echo [3] 📖 Instructions
echo [0] 🚪 Exit
echo.
set /p choice="Select option: "

if "%choice%"=="1" goto :powershell_trainer
if "%choice%"=="2" goto :process_finder
if "%choice%"=="3" goto :instructions
if "%choice%"=="0" goto :exit
goto :start
