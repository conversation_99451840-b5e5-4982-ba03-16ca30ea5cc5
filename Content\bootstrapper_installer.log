2025/09/13 08:44:46 ===LOG START 2025-09-13 08:44:46.1981705 -0500 CDT m=+0.014547601===
2025/09/13 08:44:46 Checking if the driver is in the game folder
2025/09/13 08:44:46 C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe Get-Service -Name atvi-hrist_msstore -ErrorAction SilentlyContinue
2025/09/13 08:44:46 
2025/09/13 08:44:46 Service atvi-hrist_msstore does not exist, creating service.
2025/09/13 08:44:46 Creating service atvi-hrist_msstore ...
2025/09/13 08:44:46 C:\WINDOWS\system32\sc.exe create atvi-hrist_msstore type=kernel binPath=C:\XboxGames\Call of Duty- Modern Warfare 2\Content\Randgrid.sys
2025/09/13 08:44:46 [SC] CreateService SUCCESS

2025/09/13 08:44:46 Waiting for service to be created...
2025/09/13 08:44:47 C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe Get-Service -Name atvi-hrist_msstore -ErrorAction SilentlyContinue
2025/09/13 08:44:47 
Status   Name               DisplayName                           
------   ----               -----------                           
Stopped  atvi-hrist_msstore atvi-hrist_msstore                    



2025/09/13 08:44:47 Service created successfully.
2025/09/13 08:44:47 Setting service descriptor atvi-hrist_msstore ...
2025/09/13 08:44:47 [SC] SetServiceObjectSecurity SUCCESS

2025/09/13 08:44:47 Driver is ready.
2025/09/13 08:44:47 ===LOG END===
