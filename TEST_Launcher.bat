@echo off
title MW2 Trainer Test
color 0A
cls

echo ╔══════════════════════════════════════════════════════════╗
echo ║                    MW2 TRAINER TEST                      ║
echo ╚══════════════════════════════════════════════════════════╝
echo.
echo [*] If you can see this, the batch files are working!
echo.
echo [*] Current directory: %CD%
echo.
echo [*] Available trainer files:
dir /b *.bat *.ps1 2>nul
echo.
echo [*] Testing PowerShell availability...
powershell -Command "Write-Host '[+] PowerShell is working!' -ForegroundColor Green"
echo.
echo [*] Checking for MW2 process...
tasklist | findstr /i "cod" | findstr /v "cloudcode"
if %ERRORLEVEL% EQU 0 (
    echo [+] Found COD-related processes!
) else (
    echo [-] No MW2 processes found
)
echo.
echo ════════════════════════════════════════════════════════════
echo [1] Launch PowerShell Trainer
echo [2] Launch Process Finder  
echo [3] Test PowerShell Script Directly
echo [0] Exit
echo ════════════════════════════════════════════════════════════
echo.
set /p choice="Select option: "

if "%choice%"=="1" (
    echo [*] Launching PowerShell trainer...
    powershell -ExecutionPolicy Bypass -File "MW2_PowerShell_Trainer.ps1"
)
if "%choice%"=="2" (
    echo [*] Launching process finder...
    call "MW2_Process_Finder.bat"
)
if "%choice%"=="3" (
    echo [*] Testing PowerShell script...
    powershell -ExecutionPolicy Bypass -Command "Write-Host 'PowerShell execution test successful!' -ForegroundColor Green; Read-Host 'Press Enter to continue'"
)
if "%choice%"=="0" (
    echo [*] Goodbye!
    timeout /t 2 >nul
    exit
)

echo.
echo [*] Press any key to return to menu...
pause >nul
goto :start

:start
cls
goto :0
