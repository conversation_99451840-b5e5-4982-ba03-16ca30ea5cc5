# Call of Duty: Modern Warfare II (2022) - GamePass Offline Trainer Setup Guide

## Prerequisites
- Call of Duty: Modern Warfare II (2022) - GamePass/Xbox App version
- Cheat Engine 7.0 or newer
- Game running in offline/survival mode only
- Windows 10/11 with Xbox App

## Method 1: Cheat Engine Table (Recommended for beginners)

### Download Cheat Engine Table
1. Download Cheat Engine from: https://cheatengine.org/
2. Get the MW2 2022 table from FearLess Revolution:
   - URL: https://fearlessrevolution.com/viewtopic.php?t=32107
   - File: iw4spfrf.CT (1.96 KiB)

### Setup Instructions
1. Install Cheat Engine
2. Launch MW2 2022 from Xbox App in offline mode
3. **Important**: Run Cheat Engine as Administrator (GamePass apps need elevated permissions)
4. Double-click the .CT file to open it in Cheat Engine
5. Click the PC icon in Cheat Engine
6. Look for process names:
   - "cod22-cod.exe"
   - "Call of Duty Modern Warfare II.exe"
   - May appear as different name in GamePass version
7. Activate features by checking boxes

### Available Features
- ✅ Infinite Health
- ✅ Easy Kills
- ✅ Unlimited Ammo
- ✅ Unlimited Grenades
- ✅ No Reload
- ✅ Super Accuracy
- ✅ Rapid Fire
- ✅ No Recoil

## Method 2: Custom Memory Scanner (Advanced)

For finding current offsets and creating custom trainers, we'll build tools to:
- Scan memory for health/ammo values
- Find base addresses and offsets
- Create external trainer

## Game Information (GamePass Version)
- **Process Name**: cod22-cod.exe (or similar GamePass variant)
- **Engine**: IW Engine
- **Installation Path**: C:\XboxGames\Call of Duty- Modern Warfare 2\Content\
- **Main Executable**: cod22-cod.exe
- **GamePass Specifics**:
  - May require elevated permissions
  - Process name might vary
  - UWP app sandboxing may affect some features

## Safety Notes
- ⚠️ ONLY use in offline modes (Campaign, Survival, etc.)
- ⚠️ NEVER use in multiplayer - will result in permanent ban
- ⚠️ Disable internet connection when using trainers for extra safety

## Troubleshooting
- If table doesn't work, game may have updated
- Try running as administrator
- Ensure game is fully loaded before attaching
- Some features may only work in specific game modes

## Next Steps
We'll create custom tools to find current memory addresses and build a more robust trainer.
