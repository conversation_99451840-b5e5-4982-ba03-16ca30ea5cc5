@echo off
title MW2 GamePass Process Finder
color 0A

:main
cls
echo ╔══════════════════════════════════════════════════════════╗
echo ║              MW2 GamePass Process Finder                ║
echo ╚══════════════════════════════════════════════════════════╝
echo.

echo [*] Searching for MW2 processes...
echo.

REM Check for various MW2 process names
set "found=0"

echo [*] Looking for cod22-cod.exe...
tasklist | findstr /i "cod22-cod.exe" >nul
if %ERRORLEVEL% EQU 0 (
    echo [+] ✓ Found: cod22-cod.exe
    tasklist | findstr /i "cod22-cod.exe"
    set "found=1"
) else (
    echo [-] ✗ cod22-cod.exe not found
)

echo.
echo [*] Looking for ModernWarfareII...
tasklist | findstr /i "ModernWarfareII" >nul
if %ERRORLEVEL% EQU 0 (
    echo [+] ✓ Found: ModernWarfareII process
    tasklist | findstr /i "ModernWarfareII"
    set "found=1"
) else (
    echo [-] ✗ ModernWarfareII not found
)

echo.
echo [*] Looking for any COD processes...
tasklist | findstr /i "cod" | findstr /v "cloudcode"
if %ERRORLEVEL% EQU 0 (
    set "found=1"
) else (
    echo [-] ✗ No COD processes found
)

echo.
echo ════════════════════════════════════════════════════════════
if "%found%"=="1" (
    echo [+] MW2 PROCESS FOUND! Ready for trainer.
    echo.
    echo [*] Next steps:
    echo     1. Make sure you're in OFFLINE/Survival mode
    echo     2. For full trainer, install MinGW-w64 or Visual Studio
    echo     3. Run compile_tools.bat to build the trainer
    echo.
    echo [*] Quick memory info:
    wmic process where "name like '%%cod%%'" get ProcessId,Name,WorkingSetSize 2>nul
) else (
    echo [!] MW2 NOT RUNNING
    echo.
    echo [*] To use the trainer:
    echo     1. Launch MW2 from Xbox App
    echo     2. Go to Survival mode (offline only!)
    echo     3. Run this script again
    echo.
    echo [*] All running processes with 'cod' in name:
    tasklist | findstr /i "cod"
)

echo ════════════════════════════════════════════════════════════
echo.
echo [R] Refresh / Check Again
echo [I] Install Compiler (MinGW-w64)
echo [C] Compile Trainer (if compiler available)
echo [H] Help / Instructions
echo [Q] Quit
echo.
set /p choice="Select option: "

if /i "%choice%"=="R" goto :main
if /i "%choice%"=="I" goto :install
if /i "%choice%"=="C" goto :compile
if /i "%choice%"=="H" goto :help
if /i "%choice%"=="Q" goto :quit
goto :main

:install
cls
echo [*] Installing MinGW-w64 compiler...
echo.
echo [!] This requires internet connection
echo [*] Downloading MinGW-w64...

powershell -Command "try { Invoke-WebRequest -Uri 'https://github.com/niXman/mingw-builds-binaries/releases/download/12.2.0-rt_v10-rev2/winlibs-x86_64-posix-seh-gcc-12.2.0-mingw-w64-10.0.0-r2.zip' -OutFile 'mingw.zip' -UseBasicParsing; Write-Host '[+] Download complete' } catch { Write-Host '[-] Download failed. Check internet connection.' }"

if exist mingw.zip (
    echo [*] Extracting MinGW-w64...
    powershell -Command "try { Expand-Archive -Path 'mingw.zip' -DestinationPath '.' -Force; Write-Host '[+] Extraction complete' } catch { Write-Host '[-] Extraction failed' }"
    
    if exist mingw64\bin\g++.exe (
        echo [+] MinGW-w64 installed successfully!
        echo [*] Adding to PATH for this session...
        set "PATH=%CD%\mingw64\bin;%PATH%"
        del mingw.zip 2>nul
        echo [+] Ready to compile trainer!
    ) else (
        echo [-] Installation failed
    )
) else (
    echo [-] Download failed. Please install manually:
    echo     https://www.mingw-w64.org/downloads/
)

echo.
pause
goto :main

:compile
cls
echo [*] Compiling MW2 Trainer...
echo.

REM Check if we have g++ available
where g++ >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    REM Try local mingw installation
    if exist mingw64\bin\g++.exe (
        set "PATH=%CD%\mingw64\bin;%PATH%"
        echo [+] Using local MinGW installation
    ) else (
        echo [!] No compiler found. Please install first (option I)
        pause
        goto :main
    )
)

echo [*] Compiling standalone trainer...
g++ -std=c++17 -O2 -static -o MW2_Standalone_Trainer.exe MW2_Standalone_Trainer.cpp -lpsapi 2>compile_errors.txt

if exist MW2_Standalone_Trainer.exe (
    echo [+] ✓ Compilation successful!
    echo [+] Created: MW2_Standalone_Trainer.exe
    echo.
    echo [*] You can now run the trainer:
    echo     MW2_Standalone_Trainer.exe
    del compile_errors.txt 2>nul
) else (
    echo [-] ✗ Compilation failed
    echo [*] Error details:
    type compile_errors.txt 2>nul
)

echo.
pause
goto :main

:help
cls
echo ╔══════════════════════════════════════════════════════════╗
echo ║                        HELP                              ║
echo ╚══════════════════════════════════════════════════════════╝
echo.
echo 🎯 STEP-BY-STEP GUIDE:
echo ═══════════════════════
echo 1. Launch MW2 from Xbox App
echo 2. Go to Survival mode (OFFLINE ONLY!)
echo 3. Run this script and check if MW2 is detected
echo 4. Install compiler (option I) - first time only
echo 5. Compile trainer (option C) - first time only
echo 6. Run MW2_Standalone_Trainer.exe
echo.
echo 🎮 TRAINER FEATURES:
echo ═══════════════════
echo • God Mode (never die)
echo • Infinite Ammo
echo • No Recoil
echo • Rapid Fire
echo • Auto process detection
echo.
echo ⚠️ SAFETY:
echo ═════════
echo • ONLY use in offline modes
echo • NEVER use online (permanent ban)
echo • Run as Administrator
echo.
echo 🔧 TROUBLESHOOTING:
echo ═══════════════════
echo • "Process not found" → Make sure MW2 is running
echo • "Access denied" → Run as Administrator
echo • "Compilation failed" → Install Visual Studio instead
echo.
pause
goto :main

:quit
echo [*] Goodbye!
timeout /t 2 >nul
exit
