# MW2 GamePass Simple Trainer 
using System; 
using System.Diagnostics; 
using System.Runtime.InteropServices; 
public class MemoryHelper { 
    [DllImport("kernel32.dll")] 
    public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId); 
    [DllImport("kernel32.dll")] 
    public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead); 
    [DllImport("kernel32.dll")] 
    public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int nSize, out int lpNumberOfBytesWritten); 
} 
 
Write-Host "MW2 GamePass Simple Trainer" -ForegroundColor Green 
Write-Host "Searching for MW2 process..." 
 
$process = Get-Process | Where-Object {$_.ProcessName -like "*cod22*" -or $_.ProcessName -like "*ModernWarfare*"} 
if ($process) { 
    Write-Host "Found MW2 process: $($process.ProcessName) (PID: $($process.Id))" -ForegroundColor Green 
    Write-Host "Trainer attached! Use Cheat Engine for advanced features." 
} else { 
    Write-Host "MW2 process not found. Make sure the game is running." -ForegroundColor Red 
} 
Read-Host "Press Enter to exit" 
