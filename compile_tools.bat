@echo off
echo MW2 2022 GamePass Tools Compiler
echo ================================

REM Check if Visual Studio tools are available
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [!] Visual Studio compiler not found in PATH
    echo [*] Trying to locate Visual Studio...
    
    REM Try to find and setup VS environment
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else (
        echo [!] Visual Studio not found. Please install Visual Studio or use alternative method.
        echo [*] Alternative: Use MinGW-w64 or online compiler
        goto :alternative
    )
)

echo [*] Compiling MW2 GamePass Scanner...
cl /EHsc MW2_GamePass_Scanner.cpp /Fe:MW2_Scanner.exe
if %ERRORLEVEL% EQU 0 (
    echo [+] Scanner compiled successfully: MW2_Scanner.exe
) else (
    echo [-] Scanner compilation failed
)

echo [*] Compiling MW2 GamePass Trainer...
cl /EHsc MW2_GamePass_Trainer.cpp /Fe:MW2_Trainer.exe
if %ERRORLEVEL% EQU 0 (
    echo [+] Trainer compiled successfully: MW2_Trainer.exe
) else (
    echo [-] Trainer compilation failed
)

goto :end

:alternative
echo.
echo Alternative compilation methods:
echo ================================
echo 1. Install MinGW-w64:
echo    - Download from: https://www.mingw-w64.org/
echo    - Compile with: g++ -o MW2_Scanner.exe MW2_GamePass_Scanner.cpp
echo    - Compile with: g++ -o MW2_Trainer.exe MW2_GamePass_Trainer.cpp
echo.
echo 2. Use online compiler:
echo    - Copy code to https://onlinegdb.com/
echo    - Select C++ and compile
echo.
echo 3. Install Visual Studio Community (free):
echo    - Download from: https://visualstudio.microsoft.com/vs/community/
echo    - Install with C++ development tools

:end
echo.
echo [*] Compilation complete!
echo [*] Run MW2_Scanner.exe first to find the process
echo [*] Then use MW2_Trainer.exe for basic trainer features
echo [*] For advanced features, use the Cheat Engine table
echo.
pause
