@echo off
echo ╔══════════════════════════════════════════════════════════╗
echo ║          MW2 2022 GamePass Standalone Trainer           ║
echo ║                    Auto-Compiler                        ║
echo ╚══════════════════════════════════════════════════════════╝
echo.

REM Check for MinGW first (easier to install)
where g++ >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo [+] MinGW-w64 found! Using g++ compiler...
    goto :mingw_compile
)

REM Check if Visual Studio tools are available
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [!] No compiler found in PATH
    echo [*] Trying to locate Visual Studio...

    REM Try to find and setup VS environment
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else (
        echo [!] No compiler found. Installing MinGW-w64...
        goto :install_mingw
    )
)

:mingw_compile
echo [*] Compiling with MinGW-w64...
echo [*] Compiling Standalone Trainer...
g++ -std=c++17 -O2 -static -o MW2_Standalone_Trainer.exe MW2_Standalone_Trainer.cpp -lpsapi
if %ERRORLEVEL% EQU 0 (
    echo [+] ✓ Standalone Trainer compiled: MW2_Standalone_Trainer.exe
) else (
    echo [-] ✗ Standalone Trainer compilation failed
)

echo [*] Compiling Memory Scanner...
g++ -std=c++17 -O2 -static -o MW2_Scanner.exe MW2_GamePass_Scanner.cpp -lpsapi
if %ERRORLEVEL% EQU 0 (
    echo [+] ✓ Scanner compiled: MW2_Scanner.exe
) else (
    echo [-] ✗ Scanner compilation failed
)
goto :success

echo [*] Compiling with Visual Studio...
echo [*] Compiling Standalone Trainer...
cl /EHsc /O2 MW2_Standalone_Trainer.cpp /Fe:MW2_Standalone_Trainer.exe
if %ERRORLEVEL% EQU 0 (
    echo [+] ✓ Standalone Trainer compiled: MW2_Standalone_Trainer.exe
) else (
    echo [-] ✗ Standalone Trainer compilation failed
)

echo [*] Compiling Memory Scanner...
cl /EHsc /O2 MW2_GamePass_Scanner.cpp /Fe:MW2_Scanner.exe
if %ERRORLEVEL% EQU 0 (
    echo [+] ✓ Scanner compiled: MW2_Scanner.exe
) else (
    echo [-] ✗ Scanner compilation failed
)

goto :success

:install_mingw
echo [*] Auto-installing MinGW-w64...
echo [!] This requires internet connection
powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/niXman/mingw-builds-binaries/releases/download/12.2.0-rt_v10-rev2/winlibs-x86_64-posix-seh-gcc-12.2.0-mingw-w64-10.0.0-r2.zip' -OutFile 'mingw.zip'}"
if exist mingw.zip (
    echo [+] Downloaded MinGW-w64
    powershell -Command "Expand-Archive -Path 'mingw.zip' -DestinationPath '.'"
    set PATH=%CD%\mingw64\bin;%PATH%
    del mingw.zip
    echo [+] MinGW-w64 installed locally
    goto :mingw_compile
) else (
    goto :manual_install
)

:manual_install
echo.
echo [!] Auto-install failed. Manual installation required:
echo ════════════════════════════════════════════════════════
echo 1. Download MinGW-w64:
echo    https://www.mingw-w64.org/downloads/
echo.
echo 2. Or install Visual Studio Community (free):
echo    https://visualstudio.microsoft.com/vs/community/
echo.
echo 3. Then run this script again
echo.
pause
exit /b 1

:success
echo.
echo ╔══════════════════════════════════════════════════════════╗
echo ║                   COMPILATION COMPLETE!                 ║
echo ╠══════════════════════════════════════════════════════════╣
echo ║                                                          ║
echo ║  🎯 MW2_Standalone_Trainer.exe - Main trainer           ║
echo ║     • God Mode, Infinite Ammo, No Recoil                ║
echo ║     • Auto-detects MW2 GamePass process                 ║
echo ║     • No Cheat Engine required!                         ║
echo ║                                                          ║
echo ║  🔍 MW2_Scanner.exe - Memory scanner                    ║
echo ║     • Find current addresses                            ║
echo ║     • Debug tool for advanced users                     ║
echo ║                                                          ║
echo ║  ⚠️  IMPORTANT: Only use in OFFLINE modes!              ║
echo ║                                                          ║
echo ╚══════════════════════════════════════════════════════════╝
echo.
echo [*] Ready to use! Run MW2_Standalone_Trainer.exe
echo.
pause
