# MW2 2022 GamePass GUI Trainer
# Windows Forms GUI Interface

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Memory manipulation class
Add-Type -TypeDefinition @"
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

public class MemoryHelper {
    [DllImport("kernel32.dll")]
    public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId);

    [DllImport("kernel32.dll")]
    public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead);

    [DllImport("kernel32.dll")]
    public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int nSize, out int lpNumberOfBytesWritten);

    [DllImport("kernel32.dll")]
    public static extern bool CloseHandle(IntPtr hObject);

    public const int PROCESS_ALL_ACCESS = 0x1F0FFF;
    public const int PROCESS_VM_READ = 0x0010;
    public const int PROCESS_VM_WRITE = 0x0020;
    public const int PROCESS_VM_OPERATION = 0x0008;
    public const int PROCESS_QUERY_INFORMATION = 0x0400;
}
"@

# Global variables
$global:ProcessHandle = [IntPtr]::Zero
$global:ProcessId = 0
$global:BaseAddress = [IntPtr]::Zero
$global:GodModeActive = $false
$global:InfiniteAmmoActive = $false
$global:NoRecoilActive = $false

# Function to find MW2 process
function Find-MW2Process {
    $processNames = @("cod22-cod", "ModernWarfareII", "Call of Duty Modern Warfare II")

    foreach ($name in $processNames) {
        $processes = Get-Process -Name $name -ErrorAction SilentlyContinue
        if ($processes) {
            $global:ProcessId = $processes[0].Id
            return Attach-ToProcess
        }
    }

    # Try partial matches
    $processes = Get-Process | Where-Object { $_.ProcessName -like "*cod*" -and $_.ProcessName -notlike "*cloudcode*" }
    if ($processes) {
        $global:ProcessId = $processes[0].Id
        return Attach-ToProcess
    }

    return $false
}

# Function to attach to process
function Attach-ToProcess {
    $permissions = @(
        [MemoryHelper]::PROCESS_ALL_ACCESS,
        ([MemoryHelper]::PROCESS_VM_READ -bor [MemoryHelper]::PROCESS_VM_WRITE -bor [MemoryHelper]::PROCESS_VM_OPERATION -bor [MemoryHelper]::PROCESS_QUERY_INFORMATION)
    )

    foreach ($perm in $permissions) {
        $global:ProcessHandle = [MemoryHelper]::OpenProcess($perm, $false, $global:ProcessId)
        if ($global:ProcessHandle -ne [IntPtr]::Zero) {
            Get-BaseAddress
            return $true
        }
    }
    return $false
}

# Function to get base address
function Get-BaseAddress {
    try {
        $process = Get-Process -Id $global:ProcessId
        $global:BaseAddress = $process.MainModule.BaseAddress
    } catch {
        # Ignore errors
    }
}

# Function to write memory
function Write-Memory([IntPtr]$address, [byte[]]$data) {
    $bytesWritten = 0
    return [MemoryHelper]::WriteProcessMemory($global:ProcessHandle, $address, $data, $data.Length, [ref]$bytesWritten)
}

# Create the main form
$form = New-Object System.Windows.Forms.Form
$form.Text = "MW2 2022 GamePass Trainer"
$form.Size = New-Object System.Drawing.Size(500, 600)
$form.StartPosition = "CenterScreen"
$form.FormBorderStyle = "FixedDialog"
$form.MaximizeBox = $false
$form.BackColor = [System.Drawing.Color]::FromArgb(30, 30, 30)
$form.ForeColor = [System.Drawing.Color]::White

# Title label
$titleLabel = New-Object System.Windows.Forms.Label
$titleLabel.Text = "MW2 2022 GamePass Trainer"
$titleLabel.Font = New-Object System.Drawing.Font("Arial", 16, [System.Drawing.FontStyle]::Bold)
$titleLabel.ForeColor = [System.Drawing.Color]::Lime
$titleLabel.Location = New-Object System.Drawing.Point(50, 20)
$titleLabel.Size = New-Object System.Drawing.Size(400, 30)
$titleLabel.TextAlign = "MiddleCenter"
$form.Controls.Add($titleLabel)

# Status panel
$statusPanel = New-Object System.Windows.Forms.Panel
$statusPanel.Location = New-Object System.Drawing.Point(20, 60)
$statusPanel.Size = New-Object System.Drawing.Size(450, 100)
$statusPanel.BorderStyle = "FixedSingle"
$statusPanel.BackColor = [System.Drawing.Color]::FromArgb(40, 40, 40)
$form.Controls.Add($statusPanel)

# Process status label
$processStatusLabel = New-Object System.Windows.Forms.Label
$processStatusLabel.Text = "Process: DISCONNECTED"
$processStatusLabel.Font = New-Object System.Drawing.Font("Consolas", 10)
$processStatusLabel.ForeColor = [System.Drawing.Color]::Red
$processStatusLabel.Location = New-Object System.Drawing.Point(10, 10)
$processStatusLabel.Size = New-Object System.Drawing.Size(300, 20)
$statusPanel.Controls.Add($processStatusLabel)

# PID label
$pidLabel = New-Object System.Windows.Forms.Label
$pidLabel.Text = "PID: 0"
$pidLabel.Font = New-Object System.Drawing.Font("Consolas", 10)
$pidLabel.ForeColor = [System.Drawing.Color]::White
$pidLabel.Location = New-Object System.Drawing.Point(10, 35)
$pidLabel.Size = New-Object System.Drawing.Size(200, 20)
$statusPanel.Controls.Add($pidLabel)

# Base address label
$baseLabel = New-Object System.Windows.Forms.Label
$baseLabel.Text = "Base: 0x0"
$baseLabel.Font = New-Object System.Drawing.Font("Consolas", 10)
$baseLabel.ForeColor = [System.Drawing.Color]::White
$baseLabel.Location = New-Object System.Drawing.Point(10, 60)
$baseLabel.Size = New-Object System.Drawing.Size(300, 20)
$statusPanel.Controls.Add($baseLabel)

# Connect button
$connectButton = New-Object System.Windows.Forms.Button
$connectButton.Text = "🔍 Find MW2 Process"
$connectButton.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
$connectButton.Location = New-Object System.Drawing.Point(20, 180)
$connectButton.Size = New-Object System.Drawing.Size(450, 40)
$connectButton.BackColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
$connectButton.ForeColor = [System.Drawing.Color]::White
$connectButton.FlatStyle = "Flat"
$form.Controls.Add($connectButton)

# Features panel
$featuresPanel = New-Object System.Windows.Forms.Panel
$featuresPanel.Location = New-Object System.Drawing.Point(20, 240)
$featuresPanel.Size = New-Object System.Drawing.Size(450, 250)
$featuresPanel.BorderStyle = "FixedSingle"
$featuresPanel.BackColor = [System.Drawing.Color]::FromArgb(40, 40, 40)
$form.Controls.Add($featuresPanel)

# Features title
$featuresTitle = New-Object System.Windows.Forms.Label
$featuresTitle.Text = "TRAINER FEATURES"
$featuresTitle.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
$featuresTitle.ForeColor = [System.Drawing.Color]::Lime
$featuresTitle.Location = New-Object System.Drawing.Point(10, 10)
$featuresTitle.Size = New-Object System.Drawing.Size(200, 25)
$featuresPanel.Controls.Add($featuresTitle)

# God Mode button
$godModeButton = New-Object System.Windows.Forms.Button
$godModeButton.Text = "🛡️ God Mode: OFF"
$godModeButton.Font = New-Object System.Drawing.Font("Arial", 11)
$godModeButton.Location = New-Object System.Drawing.Point(20, 45)
$godModeButton.Size = New-Object System.Drawing.Size(200, 35)
$godModeButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
$godModeButton.ForeColor = [System.Drawing.Color]::White
$godModeButton.FlatStyle = "Flat"
$featuresPanel.Controls.Add($godModeButton)

# Infinite Ammo button
$infiniteAmmoButton = New-Object System.Windows.Forms.Button
$infiniteAmmoButton.Text = "🔫 Infinite Ammo: OFF"
$infiniteAmmoButton.Font = New-Object System.Drawing.Font("Arial", 11)
$infiniteAmmoButton.Location = New-Object System.Drawing.Point(230, 45)
$infiniteAmmoButton.Size = New-Object System.Drawing.Size(200, 35)
$infiniteAmmoButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
$infiniteAmmoButton.ForeColor = [System.Drawing.Color]::White
$infiniteAmmoButton.FlatStyle = "Flat"
$featuresPanel.Controls.Add($infiniteAmmoButton)

# No Recoil button
$noRecoilButton = New-Object System.Windows.Forms.Button
$noRecoilButton.Text = "🎯 No Recoil: OFF"
$noRecoilButton.Font = New-Object System.Drawing.Font("Arial", 11)
$noRecoilButton.Location = New-Object System.Drawing.Point(20, 90)
$noRecoilButton.Size = New-Object System.Drawing.Size(200, 35)
$noRecoilButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
$noRecoilButton.ForeColor = [System.Drawing.Color]::White
$noRecoilButton.FlatStyle = "Flat"
$featuresPanel.Controls.Add($noRecoilButton)

# Process Info button
$processInfoButton = New-Object System.Windows.Forms.Button
$processInfoButton.Text = "📊 Process Info"
$processInfoButton.Font = New-Object System.Drawing.Font("Arial", 11)
$processInfoButton.Location = New-Object System.Drawing.Point(230, 90)
$processInfoButton.Size = New-Object System.Drawing.Size(200, 35)
$processInfoButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
$processInfoButton.ForeColor = [System.Drawing.Color]::White
$processInfoButton.FlatStyle = "Flat"
$featuresPanel.Controls.Add($processInfoButton)

# Log textbox
$logTextBox = New-Object System.Windows.Forms.TextBox
$logTextBox.Multiline = $true
$logTextBox.ScrollBars = "Vertical"
$logTextBox.ReadOnly = $true
$logTextBox.Font = New-Object System.Drawing.Font("Consolas", 9)
$logTextBox.BackColor = [System.Drawing.Color]::Black
$logTextBox.ForeColor = [System.Drawing.Color]::Lime
$logTextBox.Location = New-Object System.Drawing.Point(20, 140)
$logTextBox.Size = New-Object System.Drawing.Size(410, 90)
$featuresPanel.Controls.Add($logTextBox)

# Safety warning label
$warningLabel = New-Object System.Windows.Forms.Label
$warningLabel.Text = "⚠️ WARNING: Only use in OFFLINE modes! Online usage = PERMANENT BAN!"
$warningLabel.Font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Bold)
$warningLabel.ForeColor = [System.Drawing.Color]::Red
$warningLabel.Location = New-Object System.Drawing.Point(20, 510)
$warningLabel.Size = New-Object System.Drawing.Size(450, 40)
$warningLabel.TextAlign = "MiddleCenter"
$form.Controls.Add($warningLabel)

# Function to add log message
function Add-LogMessage($message) {
    $timestamp = Get-Date -Format "HH:mm:ss"
    $logTextBox.AppendText("[$timestamp] $message`r`n")
    $logTextBox.SelectionStart = $logTextBox.Text.Length
    $logTextBox.ScrollToCaret()
}

# Function to update status
function Update-Status {
    if ($global:ProcessHandle -ne [IntPtr]::Zero) {
        $processStatusLabel.Text = "Process: CONNECTED"
        $processStatusLabel.ForeColor = [System.Drawing.Color]::Lime
        $pidLabel.Text = "PID: $global:ProcessId"
        $baseLabel.Text = "Base: 0x$($global:BaseAddress.ToString('X'))"
        $connectButton.Text = "✅ Connected to MW2"
        $connectButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
    } else {
        $processStatusLabel.Text = "Process: DISCONNECTED"
        $processStatusLabel.ForeColor = [System.Drawing.Color]::Red
        $pidLabel.Text = "PID: 0"
        $baseLabel.Text = "Base: 0x0"
        $connectButton.Text = "🔍 Find MW2 Process"
        $connectButton.BackColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
    }
}

# Event handlers
$connectButton.Add_Click({
    Add-LogMessage "Searching for MW2 process..."
    if (Find-MW2Process) {
        Add-LogMessage "Successfully connected to MW2!"
        Add-LogMessage "Process ID: $global:ProcessId"
        Add-LogMessage "Base Address: 0x$($global:BaseAddress.ToString('X'))"
        Update-Status
    } else {
        Add-LogMessage "MW2 process not found!"
        Add-LogMessage "Make sure MW2 is running in offline mode"
        Update-Status
    }
})

$godModeButton.Add_Click({
    if ($global:ProcessHandle -eq [IntPtr]::Zero) {
        Add-LogMessage "ERROR: Not connected to MW2 process!"
        return
    }

    $global:GodModeActive = -not $global:GodModeActive
    if ($global:GodModeActive) {
        $godModeButton.Text = "🛡️ God Mode: ON"
        $godModeButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
        Add-LogMessage "God Mode: ENABLED (placeholder - use Cheat Engine for full functionality)"
    } else {
        $godModeButton.Text = "🛡️ God Mode: OFF"
        $godModeButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
        Add-LogMessage "God Mode: DISABLED"
    }
})

$infiniteAmmoButton.Add_Click({
    if ($global:ProcessHandle -eq [IntPtr]::Zero) {
        Add-LogMessage "ERROR: Not connected to MW2 process!"
        return
    }

    $global:InfiniteAmmoActive = -not $global:InfiniteAmmoActive
    if ($global:InfiniteAmmoActive) {
        $infiniteAmmoButton.Text = "🔫 Infinite Ammo: ON"
        $infiniteAmmoButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
        Add-LogMessage "Infinite Ammo: ENABLED (placeholder - use Cheat Engine for full functionality)"
    } else {
        $infiniteAmmoButton.Text = "🔫 Infinite Ammo: OFF"
        $infiniteAmmoButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
        Add-LogMessage "Infinite Ammo: DISABLED"
    }
})

$noRecoilButton.Add_Click({
    if ($global:ProcessHandle -eq [IntPtr]::Zero) {
        Add-LogMessage "ERROR: Not connected to MW2 process!"
        return
    }

    $global:NoRecoilActive = -not $global:NoRecoilActive
    if ($global:NoRecoilActive) {
        # Try to apply no recoil using known offset
        $recoilOffset = 0x48C84
        $recoilAddress = [IntPtr]::Add($global:BaseAddress, $recoilOffset)
        $noRecoilBytes = [BitConverter]::GetBytes([float]0.0)

        if (Write-Memory $recoilAddress $noRecoilBytes) {
            $noRecoilButton.Text = "🎯 No Recoil: ON"
            $noRecoilButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
            Add-LogMessage "No Recoil: ENABLED successfully!"
        } else {
            $global:NoRecoilActive = $false
            Add-LogMessage "No Recoil: FAILED (offset may be outdated)"
        }
    } else {
        $noRecoilButton.Text = "🎯 No Recoil: OFF"
        $noRecoilButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
        Add-LogMessage "No Recoil: DISABLED"
    }
})

$processInfoButton.Add_Click({
    if ($global:ProcessHandle -eq [IntPtr]::Zero) {
        Add-LogMessage "ERROR: Not connected to MW2 process!"
        return
    }

    try {
        $process = Get-Process -Id $global:ProcessId
        Add-LogMessage "=== PROCESS INFO ==="
        Add-LogMessage "Name: $($process.ProcessName)"
        Add-LogMessage "PID: $($process.Id)"
        Add-LogMessage "Memory: $([math]::Round($process.WorkingSet64 / 1MB, 2)) MB"
        Add-LogMessage "Base: 0x$($global:BaseAddress.ToString('X'))"
        Add-LogMessage "Handle: 0x$($global:ProcessHandle.ToString('X'))"
        Add-LogMessage "==================="
    } catch {
        Add-LogMessage "ERROR: Could not get process information"
    }
})

# Initialize the form
Add-LogMessage "MW2 2022 GamePass GUI Trainer initialized"
Add-LogMessage "Click 'Find MW2 Process' to connect to the game"
Add-LogMessage "Make sure MW2 is running in OFFLINE mode only!"
Update-Status

# Show the form
$form.Add_Shown({$form.Activate()})
[System.Windows.Forms.Application]::Run($form)

# Cleanup
if ($global:ProcessHandle -ne [IntPtr]::Zero) {
    [MemoryHelper]::CloseHandle($global:ProcessHandle)
}