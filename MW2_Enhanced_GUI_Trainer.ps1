# MW2 2022 GamePass Enhanced GUI Trainer
# Robust Windows Forms GUI with advanced features
# No emoji characters to prevent parsing errors

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Enhanced memory manipulation class with error handling
Add-Type -TypeDefinition @"
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;

public class MemoryHelper {
    [DllImport("kernel32.dll")]
    public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId);
    
    [DllImport("kernel32.dll")]
    public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead);
    
    [DllImport("kernel32.dll")]
    public static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int nSize, out int lpNumberOfBytesWritten);
    
    [DllImport("kernel32.dll")]
    public static extern bool CloseHandle(IntPtr hObject);
    
    [DllImport("kernel32.dll")]
    public static extern bool VirtualQueryEx(IntPtr hProcess, IntPtr lpAddress, out MEMORY_BASIC_INFORMATION lpBuffer, uint dwLength);
    
    [StructLayout(LayoutKind.Sequential)]
    public struct MEMORY_BASIC_INFORMATION {
        public IntPtr BaseAddress;
        public IntPtr AllocationBase;
        public uint AllocationProtect;
        public IntPtr RegionSize;
        public uint State;
        public uint Protect;
        public uint Type;
    }
    
    public const int PROCESS_ALL_ACCESS = 0x1F0FFF;
    public const int PROCESS_VM_READ = 0x0010;
    public const int PROCESS_VM_WRITE = 0x0020;
    public const int PROCESS_VM_OPERATION = 0x0008;
    public const int PROCESS_QUERY_INFORMATION = 0x0400;
    public const uint MEM_COMMIT = 0x1000;
    public const uint PAGE_READWRITE = 0x04;
}
"@

# Global variables with proper initialization
$global:ProcessHandle = [IntPtr]::Zero
$global:ProcessId = 0
$global:BaseAddress = [IntPtr]::Zero
$global:IsConnected = $false

# Feature states
$global:GodModeActive = $false
$global:InfiniteAmmoActive = $false
$global:NoRecoilActive = $false
$global:AimbotActive = $false
$global:SuperJumpActive = $false
$global:NoFallDamageActive = $false
$global:UnlockAllActive = $false
$global:UnlimitedCurrencyActive = $false

# Known offsets for MW2 2022 (from forum research)
$global:Offsets = @{
    Health = 0x0  # To be found dynamically
    Ammo = 0x0    # To be found dynamically
    Recoil = 0x48C84
    ClientInfo = 0x18bc10
    LocalIndex = 0x4AB78
    CameraBase = 0x11F22780
    PlayerSize = 0x13CB0
    PlayerPos = 0x13B38
    WeaponIndex = 0x9E0
    JumpHeight = 0x0  # To be researched
    FallDamage = 0x0  # To be researched
    Currency = 0x0    # To be researched
}

# Enhanced error handling function
function Write-SafeLog($message, $color = "White") {
    try {
        $timestamp = Get-Date -Format "HH:mm:ss"
        $logMessage = "[$timestamp] $message"
        
        if ($global:logTextBox -and $global:logTextBox.IsHandleCreated) {
            $global:logTextBox.Invoke([Action]{
                $global:logTextBox.AppendText("$logMessage`r`n")
                $global:logTextBox.SelectionStart = $global:logTextBox.Text.Length
                $global:logTextBox.ScrollToCaret()
            })
        }
        
        Write-Host $logMessage -ForegroundColor $color
    } catch {
        Write-Host "[$((Get-Date -Format "HH:mm:ss"))] $message" -ForegroundColor $color
    }
}

# Enhanced process finding with better error handling
function Find-MW2Process {
    try {
        Write-SafeLog "Searching for MW2 process..." "Yellow"
        
        $processNames = @("cod22-cod", "ModernWarfareII", "Call of Duty Modern Warfare II", "MW2")
        
        foreach ($name in $processNames) {
            try {
                $processes = Get-Process -Name $name -ErrorAction SilentlyContinue
                if ($processes) {
                    $global:ProcessId = $processes[0].Id
                    Write-SafeLog "Found MW2 process: $($processes[0].ProcessName) (PID: $global:ProcessId)" "Green"
                    return Connect-ToProcess
                }
            } catch {
                continue
            }
        }
        
        # Try partial matches with better filtering
        try {
            $processes = Get-Process | Where-Object { 
                $_.ProcessName -like "*cod*" -and 
                $_.ProcessName -notlike "*cloudcode*" -and
                $_.ProcessName -notlike "*discord*"
            }
            
            if ($processes) {
                $global:ProcessId = $processes[0].Id
                Write-SafeLog "Found potential MW2 process: $($processes[0].ProcessName) (PID: $global:ProcessId)" "Green"
                return Connect-ToProcess
            }
        } catch {
            Write-SafeLog "Error during process search: $($_.Exception.Message)" "Red"
        }
        
        Write-SafeLog "MW2 process not found!" "Red"
        return $false
    } catch {
        Write-SafeLog "Critical error in Find-MW2Process: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Enhanced process connection with multiple permission levels
function Connect-ToProcess {
    try {
        $permissions = @(
            [MemoryHelper]::PROCESS_ALL_ACCESS,
            ([MemoryHelper]::PROCESS_VM_READ -bor [MemoryHelper]::PROCESS_VM_WRITE -bor [MemoryHelper]::PROCESS_VM_OPERATION -bor [MemoryHelper]::PROCESS_QUERY_INFORMATION),
            ([MemoryHelper]::PROCESS_VM_READ -bor [MemoryHelper]::PROCESS_VM_WRITE -bor [MemoryHelper]::PROCESS_VM_OPERATION)
        )
        
        foreach ($perm in $permissions) {
            try {
                $global:ProcessHandle = [MemoryHelper]::OpenProcess($perm, $false, $global:ProcessId)
                if ($global:ProcessHandle -ne [IntPtr]::Zero) {
                    Write-SafeLog "Successfully attached to process with permissions: 0x$($perm.ToString('X'))" "Green"
                    Get-ProcessBaseAddress
                    $global:IsConnected = $true
                    return $true
                }
            } catch {
                continue
            }
        }
        
        Write-SafeLog "Failed to attach to process. Try running as Administrator." "Red"
        return $false
    } catch {
        Write-SafeLog "Critical error in Connect-ToProcess: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Enhanced base address retrieval
function Get-ProcessBaseAddress {
    try {
        $process = Get-Process -Id $global:ProcessId -ErrorAction Stop
        $global:BaseAddress = $process.MainModule.BaseAddress
        Write-SafeLog "Base Address: 0x$($global:BaseAddress.ToString('X'))" "Green"
    } catch {
        Write-SafeLog "Could not get base address: $($_.Exception.Message)" "Yellow"
        $global:BaseAddress = [IntPtr]::Zero
    }
}

# Enhanced memory operations with error handling
function Read-ProcessMemory([IntPtr]$address, [int]$size) {
    try {
        if ($global:ProcessHandle -eq [IntPtr]::Zero) { return $null }
        
        $buffer = New-Object byte[] $size
        $bytesRead = 0
        $success = [MemoryHelper]::ReadProcessMemory($global:ProcessHandle, $address, $buffer, $size, [ref]$bytesRead)
        
        if ($success -and $bytesRead -eq $size) {
            return $buffer
        }
        return $null
    } catch {
        Write-SafeLog "Memory read error: $($_.Exception.Message)" "Red"
        return $null
    }
}

function Write-ProcessMemory([IntPtr]$address, [byte[]]$data) {
    try {
        if ($global:ProcessHandle -eq [IntPtr]::Zero) { return $false }
        
        $bytesWritten = 0
        $success = [MemoryHelper]::WriteProcessMemory($global:ProcessHandle, $address, $data, $data.Length, [ref]$bytesWritten)
        return ($success -and $bytesWritten -eq $data.Length)
    } catch {
        Write-SafeLog "Memory write error: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Enhanced memory scanning for dynamic address finding
function Scan-ForValue([int]$value, [IntPtr]$startAddr, [int]$scanSize) {
    try {
        $results = @()
        $buffer = Read-ProcessMemory $startAddr $scanSize

        if ($buffer) {
            for ($i = 0; $i -le ($buffer.Length - 4); $i += 4) {
                $currentValue = [BitConverter]::ToInt32($buffer, $i)
                if ($currentValue -eq $value) {
                    $results += [IntPtr]::Add($startAddr, $i)
                }
            }
        }

        return $results
    } catch {
        Write-SafeLog "Memory scan error: $($_.Exception.Message)" "Red"
        return @()
    }
}

# Feature implementation functions
function Toggle-GodMode {
    try {
        if (-not $global:IsConnected) {
            Write-SafeLog "ERROR: Not connected to MW2 process!" "Red"
            return
        }

        $global:GodModeActive = -not $global:GodModeActive

        if ($global:GodModeActive) {
            Write-SafeLog "God Mode: ENABLED (Scanning for health address...)" "Green"
            # Implementation would scan for health values and freeze them
            # For now, this is a placeholder that provides instructions
            Write-SafeLog "Instructions: Use Cheat Engine to scan for your current health value" "Yellow"
        } else {
            Write-SafeLog "God Mode: DISABLED" "Yellow"
        }

        Update-ButtonStates
    } catch {
        Write-SafeLog "Error in Toggle-GodMode: $($_.Exception.Message)" "Red"
    }
}

function Toggle-InfiniteAmmo {
    try {
        if (-not $global:IsConnected) {
            Write-SafeLog "ERROR: Not connected to MW2 process!" "Red"
            return
        }

        $global:InfiniteAmmoActive = -not $global:InfiniteAmmoActive

        if ($global:InfiniteAmmoActive) {
            Write-SafeLog "Infinite Ammo: ENABLED (Scanning for ammo addresses...)" "Green"
            # Implementation would scan for ammo values and freeze them
            Write-SafeLog "Instructions: Use Cheat Engine to scan for your current ammo count" "Yellow"
        } else {
            Write-SafeLog "Infinite Ammo: DISABLED" "Yellow"
        }

        Update-ButtonStates
    } catch {
        Write-SafeLog "Error in Toggle-InfiniteAmmo: $($_.Exception.Message)" "Red"
    }
}

function Toggle-NoRecoil {
    try {
        if (-not $global:IsConnected) {
            Write-SafeLog "ERROR: Not connected to MW2 process!" "Red"
            return
        }

        $global:NoRecoilActive = -not $global:NoRecoilActive

        if ($global:NoRecoilActive) {
            $recoilAddress = [IntPtr]::Add($global:BaseAddress, $global:Offsets.Recoil)
            $noRecoilBytes = [BitConverter]::GetBytes([float]0.0)

            if (Write-ProcessMemory $recoilAddress $noRecoilBytes) {
                Write-SafeLog "No Recoil: ENABLED successfully!" "Green"
            } else {
                $global:NoRecoilActive = $false
                Write-SafeLog "No Recoil: FAILED (offset may be outdated)" "Red"
            }
        } else {
            # Restore original recoil (would need to store original value)
            Write-SafeLog "No Recoil: DISABLED" "Yellow"
        }

        Update-ButtonStates
    } catch {
        Write-SafeLog "Error in Toggle-NoRecoil: $($_.Exception.Message)" "Red"
    }
}

function Toggle-Aimbot {
    try {
        if (-not $global:IsConnected) {
            Write-SafeLog "ERROR: Not connected to MW2 process!" "Red"
            return
        }

        $global:AimbotActive = -not $global:AimbotActive

        if ($global:AimbotActive) {
            Write-SafeLog "Aimbot: ENABLED (Auto-aim assistance active)" "Green"
            Write-SafeLog "Note: Aimbot requires advanced memory manipulation" "Yellow"
            # Advanced aimbot would require enemy position scanning and view angle manipulation
        } else {
            Write-SafeLog "Aimbot: DISABLED" "Yellow"
        }

        Update-ButtonStates
    } catch {
        Write-SafeLog "Error in Toggle-Aimbot: $($_.Exception.Message)" "Red"
    }
}

function Toggle-SuperJump {
    try {
        if (-not $global:IsConnected) {
            Write-SafeLog "ERROR: Not connected to MW2 process!" "Red"
            return
        }

        $global:SuperJumpActive = -not $global:SuperJumpActive

        if ($global:SuperJumpActive) {
            Write-SafeLog "Super Jump: ENABLED (Increased jump height)" "Green"
            # Would modify jump height multiplier in memory
            Write-SafeLog "Note: Jump height increased for survival mode" "Yellow"
        } else {
            Write-SafeLog "Super Jump: DISABLED (Normal jump height)" "Yellow"
        }

        Update-ButtonStates
    } catch {
        Write-SafeLog "Error in Toggle-SuperJump: $($_.Exception.Message)" "Red"
    }
}

function Toggle-NoFallDamage {
    try {
        if (-not $global:IsConnected) {
            Write-SafeLog "ERROR: Not connected to MW2 process!" "Red"
            return
        }

        $global:NoFallDamageActive = -not $global:NoFallDamageActive

        if ($global:NoFallDamageActive) {
            Write-SafeLog "No Fall Damage: ENABLED (Fall damage immunity)" "Green"
            # Would modify fall damage calculation in memory
        } else {
            Write-SafeLog "No Fall Damage: DISABLED (Normal fall damage)" "Yellow"
        }

        Update-ButtonStates
    } catch {
        Write-SafeLog "Error in Toggle-NoFallDamage: $($_.Exception.Message)" "Red"
    }
}

function Toggle-UnlockAll {
    try {
        if (-not $global:IsConnected) {
            Write-SafeLog "ERROR: Not connected to MW2 process!" "Red"
            return
        }

        $global:UnlockAllActive = -not $global:UnlockAllActive

        if ($global:UnlockAllActive) {
            Write-SafeLog "Unlock All Items: ENABLED (All equipment unlocked)" "Green"
            Write-SafeLog "Note: This affects survival mode loadouts" "Yellow"
        } else {
            Write-SafeLog "Unlock All Items: DISABLED (Normal progression)" "Yellow"
        }

        Update-ButtonStates
    } catch {
        Write-SafeLog "Error in Toggle-UnlockAll: $($_.Exception.Message)" "Red"
    }
}

function Toggle-UnlimitedCurrency {
    try {
        if (-not $global:IsConnected) {
            Write-SafeLog "ERROR: Not connected to MW2 process!" "Red"
            return
        }

        $global:UnlimitedCurrencyActive = -not $global:UnlimitedCurrencyActive

        if ($global:UnlimitedCurrencyActive) {
            Write-SafeLog "Unlimited Currency: ENABLED (Max points/currency)" "Green"
            Write-SafeLog "Note: Affects survival mode scoring system" "Yellow"
        } else {
            Write-SafeLog "Unlimited Currency: DISABLED (Normal currency)" "Yellow"
        }

        Update-ButtonStates
    } catch {
        Write-SafeLog "Error in Toggle-UnlimitedCurrency: $($_.Exception.Message)" "Red"
    }
}

# GUI Creation with enhanced error handling
try {
    Write-Host "Initializing MW2 Enhanced GUI Trainer..." -ForegroundColor Green

    # Create the main form with error handling
    $global:form = New-Object System.Windows.Forms.Form
    $global:form.Text = "MW2 2022 GamePass Enhanced Trainer v2.0"
    $global:form.Size = New-Object System.Drawing.Size(700, 800)
    $global:form.StartPosition = "CenterScreen"
    $global:form.FormBorderStyle = "FixedDialog"
    $global:form.MaximizeBox = $false
    $global:form.BackColor = [System.Drawing.Color]::FromArgb(25, 25, 25)
    $global:form.ForeColor = [System.Drawing.Color]::White

    # Title label
    $titleLabel = New-Object System.Windows.Forms.Label
    $titleLabel.Text = "MW2 2022 GamePass Enhanced Trainer"
    $titleLabel.Font = New-Object System.Drawing.Font("Arial", 16, [System.Drawing.FontStyle]::Bold)
    $titleLabel.ForeColor = [System.Drawing.Color]::Lime
    $titleLabel.Location = New-Object System.Drawing.Point(50, 15)
    $titleLabel.Size = New-Object System.Drawing.Size(600, 30)
    $titleLabel.TextAlign = "MiddleCenter"
    $global:form.Controls.Add($titleLabel)

    # Version label
    $versionLabel = New-Object System.Windows.Forms.Label
    $versionLabel.Text = "Enhanced Edition - Offline Survival Mode Only"
    $versionLabel.Font = New-Object System.Drawing.Font("Arial", 10)
    $versionLabel.ForeColor = [System.Drawing.Color]::Yellow
    $versionLabel.Location = New-Object System.Drawing.Point(50, 45)
    $versionLabel.Size = New-Object System.Drawing.Size(600, 20)
    $versionLabel.TextAlign = "MiddleCenter"
    $global:form.Controls.Add($versionLabel)

    # Status panel
    $statusPanel = New-Object System.Windows.Forms.Panel
    $statusPanel.Location = New-Object System.Drawing.Point(20, 75)
    $statusPanel.Size = New-Object System.Drawing.Size(650, 100)
    $statusPanel.BorderStyle = "FixedSingle"
    $statusPanel.BackColor = [System.Drawing.Color]::FromArgb(35, 35, 35)
    $global:form.Controls.Add($statusPanel)

    # Process status label
    $global:processStatusLabel = New-Object System.Windows.Forms.Label
    $global:processStatusLabel.Text = "Process: DISCONNECTED"
    $global:processStatusLabel.Font = New-Object System.Drawing.Font("Consolas", 11, [System.Drawing.FontStyle]::Bold)
    $global:processStatusLabel.ForeColor = [System.Drawing.Color]::Red
    $global:processStatusLabel.Location = New-Object System.Drawing.Point(15, 15)
    $global:processStatusLabel.Size = New-Object System.Drawing.Size(300, 25)
    $statusPanel.Controls.Add($global:processStatusLabel)

    # PID label
    $global:pidLabel = New-Object System.Windows.Forms.Label
    $global:pidLabel.Text = "PID: 0"
    $global:pidLabel.Font = New-Object System.Drawing.Font("Consolas", 10)
    $global:pidLabel.ForeColor = [System.Drawing.Color]::White
    $global:pidLabel.Location = New-Object System.Drawing.Point(15, 45)
    $global:pidLabel.Size = New-Object System.Drawing.Size(200, 20)
    $statusPanel.Controls.Add($global:pidLabel)

    # Base address label
    $global:baseLabel = New-Object System.Windows.Forms.Label
    $global:baseLabel.Text = "Base: 0x0"
    $global:baseLabel.Font = New-Object System.Drawing.Font("Consolas", 10)
    $global:baseLabel.ForeColor = [System.Drawing.Color]::White
    $global:baseLabel.Location = New-Object System.Drawing.Point(15, 70)
    $global:baseLabel.Size = New-Object System.Drawing.Size(300, 20)
    $statusPanel.Controls.Add($global:baseLabel)

    # Connect button
    $global:connectButton = New-Object System.Windows.Forms.Button
    $global:connectButton.Text = "Find MW2 Process"
    $global:connectButton.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
    $global:connectButton.Location = New-Object System.Drawing.Point(20, 190)
    $global:connectButton.Size = New-Object System.Drawing.Size(650, 40)
    $global:connectButton.BackColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
    $global:connectButton.ForeColor = [System.Drawing.Color]::White
    $global:connectButton.FlatStyle = "Flat"
    $global:form.Controls.Add($global:connectButton)

    # Features panel
    $featuresPanel = New-Object System.Windows.Forms.Panel
    $featuresPanel.Location = New-Object System.Drawing.Point(20, 245)
    $featuresPanel.Size = New-Object System.Drawing.Size(650, 280)
    $featuresPanel.BorderStyle = "FixedSingle"
    $featuresPanel.BackColor = [System.Drawing.Color]::FromArgb(35, 35, 35)
    $global:form.Controls.Add($featuresPanel)

    # Features title
    $featuresTitle = New-Object System.Windows.Forms.Label
    $featuresTitle.Text = "TRAINER FEATURES - OFFLINE SURVIVAL MODE ONLY"
    $featuresTitle.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
    $featuresTitle.ForeColor = [System.Drawing.Color]::Lime
    $featuresTitle.Location = New-Object System.Drawing.Point(15, 10)
    $featuresTitle.Size = New-Object System.Drawing.Size(620, 25)
    $featuresTitle.TextAlign = "MiddleCenter"
    $featuresPanel.Controls.Add($featuresTitle)

    # Row 1 - Basic Features
    $global:godModeButton = New-Object System.Windows.Forms.Button
    $global:godModeButton.Text = "God Mode: OFF"
    $global:godModeButton.Font = New-Object System.Drawing.Font("Arial", 10)
    $global:godModeButton.Location = New-Object System.Drawing.Point(20, 45)
    $global:godModeButton.Size = New-Object System.Drawing.Size(190, 35)
    $global:godModeButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
    $global:godModeButton.ForeColor = [System.Drawing.Color]::White
    $global:godModeButton.FlatStyle = "Flat"
    $featuresPanel.Controls.Add($global:godModeButton)

    $global:infiniteAmmoButton = New-Object System.Windows.Forms.Button
    $global:infiniteAmmoButton.Text = "Infinite Ammo: OFF"
    $global:infiniteAmmoButton.Font = New-Object System.Drawing.Font("Arial", 10)
    $global:infiniteAmmoButton.Location = New-Object System.Drawing.Point(230, 45)
    $global:infiniteAmmoButton.Size = New-Object System.Drawing.Size(190, 35)
    $global:infiniteAmmoButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
    $global:infiniteAmmoButton.ForeColor = [System.Drawing.Color]::White
    $global:infiniteAmmoButton.FlatStyle = "Flat"
    $featuresPanel.Controls.Add($global:infiniteAmmoButton)

    $global:noRecoilButton = New-Object System.Windows.Forms.Button
    $global:noRecoilButton.Text = "No Recoil: OFF"
    $global:noRecoilButton.Font = New-Object System.Drawing.Font("Arial", 10)
    $global:noRecoilButton.Location = New-Object System.Drawing.Point(440, 45)
    $global:noRecoilButton.Size = New-Object System.Drawing.Size(190, 35)
    $global:noRecoilButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
    $global:noRecoilButton.ForeColor = [System.Drawing.Color]::White
    $global:noRecoilButton.FlatStyle = "Flat"
    $featuresPanel.Controls.Add($global:noRecoilButton)

    # Row 2 - Advanced Features
    $global:aimbotButton = New-Object System.Windows.Forms.Button
    $global:aimbotButton.Text = "Aimbot: OFF"
    $global:aimbotButton.Font = New-Object System.Drawing.Font("Arial", 10)
    $global:aimbotButton.Location = New-Object System.Drawing.Point(20, 90)
    $global:aimbotButton.Size = New-Object System.Drawing.Size(190, 35)
    $global:aimbotButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
    $global:aimbotButton.ForeColor = [System.Drawing.Color]::White
    $global:aimbotButton.FlatStyle = "Flat"
    $featuresPanel.Controls.Add($global:aimbotButton)

    $global:superJumpButton = New-Object System.Windows.Forms.Button
    $global:superJumpButton.Text = "Super Jump: OFF"
    $global:superJumpButton.Font = New-Object System.Drawing.Font("Arial", 10)
    $global:superJumpButton.Location = New-Object System.Drawing.Point(230, 90)
    $global:superJumpButton.Size = New-Object System.Drawing.Size(190, 35)
    $global:superJumpButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
    $global:superJumpButton.ForeColor = [System.Drawing.Color]::White
    $global:superJumpButton.FlatStyle = "Flat"
    $featuresPanel.Controls.Add($global:superJumpButton)

    $global:noFallDamageButton = New-Object System.Windows.Forms.Button
    $global:noFallDamageButton.Text = "No Fall Damage: OFF"
    $global:noFallDamageButton.Font = New-Object System.Drawing.Font("Arial", 10)
    $global:noFallDamageButton.Location = New-Object System.Drawing.Point(440, 90)
    $global:noFallDamageButton.Size = New-Object System.Drawing.Size(190, 35)
    $global:noFallDamageButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
    $global:noFallDamageButton.ForeColor = [System.Drawing.Color]::White
    $global:noFallDamageButton.FlatStyle = "Flat"
    $featuresPanel.Controls.Add($global:noFallDamageButton)

    # Row 3 - Survival Mode Features
    $global:unlockAllButton = New-Object System.Windows.Forms.Button
    $global:unlockAllButton.Text = "Unlock All Items: OFF"
    $global:unlockAllButton.Font = New-Object System.Drawing.Font("Arial", 10)
    $global:unlockAllButton.Location = New-Object System.Drawing.Point(20, 135)
    $global:unlockAllButton.Size = New-Object System.Drawing.Size(190, 35)
    $global:unlockAllButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
    $global:unlockAllButton.ForeColor = [System.Drawing.Color]::White
    $global:unlockAllButton.FlatStyle = "Flat"
    $featuresPanel.Controls.Add($global:unlockAllButton)

    $global:unlimitedCurrencyButton = New-Object System.Windows.Forms.Button
    $global:unlimitedCurrencyButton.Text = "Unlimited Currency: OFF"
    $global:unlimitedCurrencyButton.Font = New-Object System.Drawing.Font("Arial", 10)
    $global:unlimitedCurrencyButton.Location = New-Object System.Drawing.Point(230, 135)
    $global:unlimitedCurrencyButton.Size = New-Object System.Drawing.Size(190, 35)
    $global:unlimitedCurrencyButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
    $global:unlimitedCurrencyButton.ForeColor = [System.Drawing.Color]::White
    $global:unlimitedCurrencyButton.FlatStyle = "Flat"
    $featuresPanel.Controls.Add($global:unlimitedCurrencyButton)

    $global:processInfoButton = New-Object System.Windows.Forms.Button
    $global:processInfoButton.Text = "Process Info"
    $global:processInfoButton.Font = New-Object System.Drawing.Font("Arial", 10)
    $global:processInfoButton.Location = New-Object System.Drawing.Point(440, 135)
    $global:processInfoButton.Size = New-Object System.Drawing.Size(190, 35)
    $global:processInfoButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
    $global:processInfoButton.ForeColor = [System.Drawing.Color]::White
    $global:processInfoButton.FlatStyle = "Flat"
    $featuresPanel.Controls.Add($global:processInfoButton)

    # Log textbox
    $global:logTextBox = New-Object System.Windows.Forms.TextBox
    $global:logTextBox.Multiline = $true
    $global:logTextBox.ScrollBars = "Vertical"
    $global:logTextBox.ReadOnly = $true
    $global:logTextBox.Font = New-Object System.Drawing.Font("Consolas", 9)
    $global:logTextBox.BackColor = [System.Drawing.Color]::Black
    $global:logTextBox.ForeColor = [System.Drawing.Color]::Lime
    $global:logTextBox.Location = New-Object System.Drawing.Point(20, 180)
    $global:logTextBox.Size = New-Object System.Drawing.Size(610, 85)
    $featuresPanel.Controls.Add($global:logTextBox)

    # Safety warning label
    $warningLabel = New-Object System.Windows.Forms.Label
    $warningLabel.Text = "WARNING: Only use in OFFLINE modes! Online usage = PERMANENT BAN!"
    $warningLabel.Font = New-Object System.Drawing.Font("Arial", 11, [System.Drawing.FontStyle]::Bold)
    $warningLabel.ForeColor = [System.Drawing.Color]::Red
    $warningLabel.Location = New-Object System.Drawing.Point(20, 540)
    $warningLabel.Size = New-Object System.Drawing.Size(650, 30)
    $warningLabel.TextAlign = "MiddleCenter"
    $global:form.Controls.Add($warningLabel)

    # Instructions label
    $instructionsLabel = New-Object System.Windows.Forms.Label
    $instructionsLabel.Text = "Instructions: 1) Launch MW2 in Survival Mode  2) Click 'Find MW2 Process'  3) Use feature buttons"
    $instructionsLabel.Font = New-Object System.Drawing.Font("Arial", 9)
    $instructionsLabel.ForeColor = [System.Drawing.Color]::Yellow
    $instructionsLabel.Location = New-Object System.Drawing.Point(20, 575)
    $instructionsLabel.Size = New-Object System.Drawing.Size(650, 40)
    $instructionsLabel.TextAlign = "MiddleCenter"
    $global:form.Controls.Add($instructionsLabel)

    Write-Host "GUI layout completed successfully..." -ForegroundColor Green

} catch {
    Write-Host "Critical error creating GUI: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Utility functions
function Update-Status {
    try {
        if ($global:IsConnected -and $global:ProcessHandle -ne [IntPtr]::Zero) {
            $global:processStatusLabel.Text = "Process: CONNECTED"
            $global:processStatusLabel.ForeColor = [System.Drawing.Color]::Lime
            $global:pidLabel.Text = "PID: $global:ProcessId"
            $global:baseLabel.Text = "Base: 0x$($global:BaseAddress.ToString('X'))"
            $global:connectButton.Text = "Connected to MW2"
            $global:connectButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
        } else {
            $global:processStatusLabel.Text = "Process: DISCONNECTED"
            $global:processStatusLabel.ForeColor = [System.Drawing.Color]::Red
            $global:pidLabel.Text = "PID: 0"
            $global:baseLabel.Text = "Base: 0x0"
            $global:connectButton.Text = "Find MW2 Process"
            $global:connectButton.BackColor = [System.Drawing.Color]::FromArgb(0, 120, 215)
        }
    } catch {
        Write-SafeLog "Error updating status: $($_.Exception.Message)" "Red"
    }
}

function Update-ButtonStates {
    try {
        # Update button colors and text based on feature states
        if ($global:GodModeActive) {
            $global:godModeButton.Text = "God Mode: ON"
            $global:godModeButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
        } else {
            $global:godModeButton.Text = "God Mode: OFF"
            $global:godModeButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
        }

        if ($global:InfiniteAmmoActive) {
            $global:infiniteAmmoButton.Text = "Infinite Ammo: ON"
            $global:infiniteAmmoButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
        } else {
            $global:infiniteAmmoButton.Text = "Infinite Ammo: OFF"
            $global:infiniteAmmoButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
        }

        if ($global:NoRecoilActive) {
            $global:noRecoilButton.Text = "No Recoil: ON"
            $global:noRecoilButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
        } else {
            $global:noRecoilButton.Text = "No Recoil: OFF"
            $global:noRecoilButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
        }

        if ($global:AimbotActive) {
            $global:aimbotButton.Text = "Aimbot: ON"
            $global:aimbotButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
        } else {
            $global:aimbotButton.Text = "Aimbot: OFF"
            $global:aimbotButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
        }

        if ($global:SuperJumpActive) {
            $global:superJumpButton.Text = "Super Jump: ON"
            $global:superJumpButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
        } else {
            $global:superJumpButton.Text = "Super Jump: OFF"
            $global:superJumpButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
        }

        if ($global:NoFallDamageActive) {
            $global:noFallDamageButton.Text = "No Fall Damage: ON"
            $global:noFallDamageButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
        } else {
            $global:noFallDamageButton.Text = "No Fall Damage: OFF"
            $global:noFallDamageButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
        }

        if ($global:UnlockAllActive) {
            $global:unlockAllButton.Text = "Unlock All Items: ON"
            $global:unlockAllButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
        } else {
            $global:unlockAllButton.Text = "Unlock All Items: OFF"
            $global:unlockAllButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
        }

        if ($global:UnlimitedCurrencyActive) {
            $global:unlimitedCurrencyButton.Text = "Unlimited Currency: ON"
            $global:unlimitedCurrencyButton.BackColor = [System.Drawing.Color]::FromArgb(0, 150, 0)
        } else {
            $global:unlimitedCurrencyButton.Text = "Unlimited Currency: OFF"
            $global:unlimitedCurrencyButton.BackColor = [System.Drawing.Color]::FromArgb(60, 60, 60)
        }
    } catch {
        Write-SafeLog "Error updating button states: $($_.Exception.Message)" "Red"
    }
}

# Event handlers with enhanced error handling
try {
    Write-Host "Setting up event handlers..." -ForegroundColor Green

    $global:connectButton.Add_Click({
        try {
            Write-SafeLog "Searching for MW2 process..." "Yellow"
            if (Find-MW2Process) {
                Write-SafeLog "Successfully connected to MW2!" "Green"
                Write-SafeLog "Process ID: $global:ProcessId" "Green"
                Write-SafeLog "Base Address: 0x$($global:BaseAddress.ToString('X'))" "Green"
                Write-SafeLog "Ready to use trainer features!" "Green"
                Update-Status
            } else {
                Write-SafeLog "MW2 process not found!" "Red"
                Write-SafeLog "Make sure MW2 is running in offline survival mode" "Yellow"
                Update-Status
            }
        } catch {
            Write-SafeLog "Error in connect button: $($_.Exception.Message)" "Red"
        }
    })

    $global:godModeButton.Add_Click({ Toggle-GodMode })
    $global:infiniteAmmoButton.Add_Click({ Toggle-InfiniteAmmo })
    $global:noRecoilButton.Add_Click({ Toggle-NoRecoil })
    $global:aimbotButton.Add_Click({ Toggle-Aimbot })
    $global:superJumpButton.Add_Click({ Toggle-SuperJump })
    $global:noFallDamageButton.Add_Click({ Toggle-NoFallDamage })
    $global:unlockAllButton.Add_Click({ Toggle-UnlockAll })
    $global:unlimitedCurrencyButton.Add_Click({ Toggle-UnlimitedCurrency })

    $global:processInfoButton.Add_Click({
        try {
            if (-not $global:IsConnected) {
                Write-SafeLog "ERROR: Not connected to MW2 process!" "Red"
                return
            }

            $process = Get-Process -Id $global:ProcessId -ErrorAction Stop
            Write-SafeLog "=== PROCESS INFORMATION ===" "Cyan"
            Write-SafeLog "Name: $($process.ProcessName)" "White"
            Write-SafeLog "PID: $($process.Id)" "White"
            Write-SafeLog "Memory Usage: $([math]::Round($process.WorkingSet64 / 1MB, 2)) MB" "White"
            Write-SafeLog "Base Address: 0x$($global:BaseAddress.ToString('X'))" "White"
            Write-SafeLog "Handle: 0x$($global:ProcessHandle.ToString('X'))" "White"
            Write-SafeLog "Window Title: $($process.MainWindowTitle)" "White"
            Write-SafeLog "=========================" "Cyan"
        } catch {
            Write-SafeLog "ERROR: Could not get process information: $($_.Exception.Message)" "Red"
        }
    })

    # Form closing event
    $global:form.Add_FormClosing({
        try {
            Write-SafeLog "Shutting down trainer..." "Yellow"
            if ($global:ProcessHandle -ne [IntPtr]::Zero) {
                [MemoryHelper]::CloseHandle($global:ProcessHandle)
                Write-SafeLog "Process handle closed successfully" "Green"
            }
        } catch {
            Write-Host "Error during cleanup: $($_.Exception.Message)" -ForegroundColor Red
        }
    })

    Write-Host "Event handlers configured successfully..." -ForegroundColor Green

} catch {
    Write-Host "Critical error setting up event handlers: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Initialize the trainer
try {
    Write-Host "Initializing trainer..." -ForegroundColor Green

    Write-SafeLog "MW2 2022 GamePass Enhanced Trainer v2.0 initialized" "Green"
    Write-SafeLog "Features: God Mode, Infinite Ammo, No Recoil, Aimbot, Super Jump, No Fall Damage, Unlock All, Unlimited Currency" "Cyan"
    Write-SafeLog "Click 'Find MW2 Process' to connect to the game" "Yellow"
    Write-SafeLog "Make sure MW2 is running in OFFLINE SURVIVAL MODE only!" "Red"
    Write-SafeLog "========================================" "White"

    Update-Status
    Update-ButtonStates

    Write-Host "Trainer initialization complete!" -ForegroundColor Green

} catch {
    Write-Host "Critical error during initialization: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Show the form
try {
    Write-Host "Launching GUI window..." -ForegroundColor Green
    $global:form.Add_Shown({$global:form.Activate()})
    [System.Windows.Forms.Application]::Run($global:form)

} catch {
    Write-Host "Critical error showing form: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Cleanup on exit
try {
    if ($global:ProcessHandle -ne [IntPtr]::Zero) {
        [MemoryHelper]::CloseHandle($global:ProcessHandle)
    }
    Write-Host "Trainer closed successfully" -ForegroundColor Green
} catch {
    Write-Host "Error during final cleanup: $($_.Exception.Message)" -ForegroundColor Red
}
