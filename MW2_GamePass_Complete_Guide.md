# MW2 2022 GamePass - Complete Offline Trainer Guide

## 🎯 What You Get
- **Cheat Engine Table**: Ready-to-use with 8+ features
- **Memory Scanner**: Find current addresses and offsets  
- **Custom Trainer**: Basic external trainer with menu
- **GamePass Compatibility**: Specifically designed for Xbox App version

## 🚀 Quick Start (Easiest Method)

### Method 1: Cheat Engine Table (Recommended)
1. **Download Cheat Engine**: https://cheatengine.org/
2. **Get the Table**: Download `iw4spfrf.CT` from FearLess Revolution
3. **Setup**:
   - Launch MW2 2022 from Xbox App (OFFLINE MODE ONLY)
   - Run Cheat Engine as Administrator
   - Open the .CT file in Cheat Engine
   - Select MW2 process (cod22-cod.exe)
   - Check boxes to enable features

### Available Features:
- ✅ **Infinite Health** - Never die
- ✅ **Easy Kills** - One-shot enemies  
- ✅ **Unlimited Ammo** - Never run out
- ✅ **Unlimited Grenades** - Infinite explosives
- ✅ **No Reload** - Skip reload animations
- ✅ **Super Accuracy** - Perfect aim
- ✅ **Rapid Fire** - Faster shooting
- ✅ **No Recoil** - No weapon kickback

## 🔧 Advanced Method: Custom Tools

### Step 1: Compile the Tools
```batch
# Run the compilation script
compile_tools.bat
```

### Step 2: Use the Scanner
```batch
# Find MW2 process and get base address
MW2_Scanner.exe
```

### Step 3: Use the Trainer
```batch
# Basic trainer with menu interface
MW2_Trainer.exe
```

## 🎮 GamePass Specific Notes

### Process Names to Look For:
- `cod22-cod.exe` (most common)
- `Call of Duty Modern Warfare II.exe`
- `ModernWarfareII.exe`

### GamePass Permissions:
- **Always run tools as Administrator**
- GamePass apps use UWP sandboxing
- Some memory regions may be protected
- Process names might vary between updates

### Troubleshooting GamePass Issues:
1. **"Process not found"**:
   - Make sure game is fully loaded
   - Try different process names
   - Check Task Manager for exact name

2. **"Access denied"**:
   - Run as Administrator
   - Disable Windows Defender temporarily
   - Check if game is actually running

3. **"Features don't work"**:
   - Game may have updated (offsets changed)
   - Try Cheat Engine table instead
   - Verify you're in offline mode

## 🛡️ Safety Guidelines

### ✅ SAFE to use in:
- **Campaign Mode** (Single-player story)
- **Survival Mode** (Offline co-op)
- **Private/Local matches**
- **Any offline content**

### ❌ NEVER use in:
- **Multiplayer** (Online matches)
- **Warzone** (Battle Royale)
- **Any online mode**
- **When connected to internet** (for extra safety)

### 🔒 Extra Safety Tips:
1. **Disconnect internet** when using trainers
2. **Use offline mode only**
3. **Don't stream/record** while using cheats
4. **Keep tools private** (don't share screenshots with cheats visible)

## 📁 File Structure
```
MW2_GamePass_Tools/
├── MW2_2022_Setup_Guide.md          # Basic setup guide
├── MW2_GamePass_Complete_Guide.md   # This comprehensive guide
├── MW2_GamePass_Scanner.cpp         # Memory scanner source
├── MW2_GamePass_Trainer.cpp         # Basic trainer source
├── compile_tools.bat                # Compilation script
├── MW2_Scanner.exe                  # Compiled scanner (after compilation)
└── MW2_Trainer.exe                  # Compiled trainer (after compilation)
```

## 🔍 Finding Your Own Offsets

If the provided offsets don't work (game updated), here's how to find new ones:

### Health Address:
1. Open Cheat Engine, attach to MW2
2. Scan for your current health (e.g., 100)
3. Take damage, scan for new value (e.g., 75)
4. Repeat until you find the address
5. Right-click → "Find out what accesses this address"

### Ammo Address:
1. Note your current ammo count
2. Scan for that value in Cheat Engine
3. Shoot a few bullets, scan for new count
4. Repeat until you find the address

### Advanced Offset Finding:
1. Use the scanner tool to get base address
2. Calculate offset: `found_address - base_address`
3. Test offset in different game sessions
4. Document working offsets for future use

## 🆘 Support & Updates

### If Tools Don't Work:
1. **Check game version** - Tools may need updates after game patches
2. **Try Cheat Engine table** - Usually more reliable
3. **Run as Administrator** - Required for GamePass apps
4. **Verify offline mode** - Online modes won't work

### Getting Help:
- Check UnknownCheats forum for updated offsets
- Use Cheat Engine community tutorials
- GamePass-specific issues: Check Xbox App forums

## ⚠️ Legal Disclaimer
These tools are for educational purposes and offline single-player use only. Using cheats in online multiplayer modes violates terms of service and will result in permanent account bans. Use at your own risk.
