#include <windows.h>
#include <tlhelp32.h>
#include <iostream>
#include <vector>
#include <string>
#include <iomanip>

class MW2GamePassScanner {
private:
    HANDLE hProcess;
    DWORD processId;
    uintptr_t baseAddress;
    
public:
    MW2GamePassScanner() : hProcess(nullptr), processId(0), baseAddress(0) {}
    
    ~MW2GamePassScanner() {
        if (hProcess) CloseHandle(hProcess);
    }
    
    // Find MW2 process (GamePass version may have different names)
    bool FindMW2Process() {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) return false;
        
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);
        
        std::vector<std::string> possibleNames = {
            "cod22-cod.exe",
            "Call of Duty Modern Warfare II.exe",
            "ModernWarfareII.exe",
            "MW2.exe"
        };
        
        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                for (const auto& name : possibleNames) {
                    if (processName.find(name) != std::string::npos) {
                        processId = pe32.th32ProcessID;
                        std::cout << "[+] Found MW2 process: " << processName 
                                  << " (PID: " << processId << ")" << std::endl;
                        CloseHandle(hSnapshot);
                        return true;
                    }
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        
        CloseHandle(hSnapshot);
        std::cout << "[-] MW2 process not found. Make sure the game is running." << std::endl;
        return false;
    }
    
    // Open process with appropriate permissions for GamePass
    bool OpenGameProcess() {
        if (processId == 0) return false;
        
        // Try different permission levels for GamePass compatibility
        DWORD permissions[] = {
            PROCESS_ALL_ACCESS,
            PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION,
            PROCESS_VM_READ | PROCESS_QUERY_INFORMATION
        };
        
        for (DWORD perm : permissions) {
            hProcess = OpenProcess(perm, FALSE, processId);
            if (hProcess) {
                std::cout << "[+] Process opened successfully" << std::endl;
                return true;
            }
        }
        
        std::cout << "[-] Failed to open process. Try running as Administrator." << std::endl;
        return false;
    }
    
    // Get base address of main module
    bool GetBaseAddress() {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE, processId);
        if (hSnapshot == INVALID_HANDLE_VALUE) return false;
        
        MODULEENTRY32 me32;
        me32.dwSize = sizeof(MODULEENTRY32);
        
        if (Module32First(hSnapshot, &me32)) {
            baseAddress = (uintptr_t)me32.modBaseAddr;
            std::cout << "[+] Base address: 0x" << std::hex << baseAddress << std::endl;
            CloseHandle(hSnapshot);
            return true;
        }
        
        CloseHandle(hSnapshot);
        return false;
    }
    
    // Read memory with error handling
    template<typename T>
    bool ReadMemory(uintptr_t address, T& value) {
        SIZE_T bytesRead;
        return ReadProcessMemory(hProcess, (LPCVOID)address, &value, sizeof(T), &bytesRead) 
               && bytesRead == sizeof(T);
    }
    
    // Write memory with error handling
    template<typename T>
    bool WriteMemory(uintptr_t address, T value) {
        SIZE_T bytesWritten;
        return WriteProcessMemory(hProcess, (LPVOID)address, &value, sizeof(T), &bytesWritten) 
               && bytesWritten == sizeof(T);
    }
    
    // Scan for specific value patterns
    std::vector<uintptr_t> ScanForValue(int value, uintptr_t startAddr, size_t scanSize) {
        std::vector<uintptr_t> results;
        std::vector<char> buffer(scanSize);
        SIZE_T bytesRead;
        
        if (!ReadProcessMemory(hProcess, (LPCVOID)startAddr, buffer.data(), scanSize, &bytesRead)) {
            return results;
        }
        
        for (size_t i = 0; i <= bytesRead - sizeof(int); i += 4) {
            int* ptr = (int*)(buffer.data() + i);
            if (*ptr == value) {
                results.push_back(startAddr + i);
            }
        }
        
        return results;
    }
    
    // Initialize scanner
    bool Initialize() {
        std::cout << "[*] MW2 GamePass Memory Scanner" << std::endl;
        std::cout << "[*] Searching for MW2 process..." << std::endl;
        
        if (!FindMW2Process()) return false;
        if (!OpenGameProcess()) return false;
        if (!GetBaseAddress()) return false;
        
        std::cout << "[+] Scanner initialized successfully!" << std::endl;
        return true;
    }
    
    // Get process handle for external use
    HANDLE GetProcessHandle() { return hProcess; }
    uintptr_t GetBaseAddr() { return baseAddress; }
    DWORD GetProcessId() { return processId; }
};

// Main function for testing
int main() {
    MW2GamePassScanner scanner;
    
    if (!scanner.Initialize()) {
        std::cout << "[-] Failed to initialize scanner" << std::endl;
        system("pause");
        return 1;
    }
    
    std::cout << "\n[*] Scanner ready! You can now:" << std::endl;
    std::cout << "1. Use Cheat Engine with process ID: " << scanner.GetProcessId() << std::endl;
    std::cout << "2. Base address for calculations: 0x" << std::hex << scanner.GetBaseAddr() << std::endl;
    std::cout << "\n[*] Press any key to exit..." << std::endl;
    
    system("pause");
    return 0;
}
